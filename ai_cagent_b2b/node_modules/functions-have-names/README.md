# functions-have-names <sup>[![Version Badge][npm-version-svg]][package-url]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![dependency status][deps-svg]][deps-url]
[![dev dependency status][dev-deps-svg]][dev-deps-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][npm-badge-png]][package-url]

Does this JS environment support the `name` property on functions?

## Example

```js
var functionsHaveNames = require('functions-have-names');
var assert = require('assert');

assert.equal(functionsHaveNames(), true); // will be `false` in IE 6-8
```

## Tests
Simply clone the repo, `npm install`, and run `npm test`

[package-url]: https://npmjs.org/package/functions-have-names
[npm-version-svg]: https://versionbadg.es/inspect-js/functions-have-names.svg
[deps-svg]: https://david-dm.org/inspect-js/functions-have-names.svg
[deps-url]: https://david-dm.org/inspect-js/functions-have-names
[dev-deps-svg]: https://david-dm.org/inspect-js/functions-have-names/dev-status.svg
[dev-deps-url]: https://david-dm.org/inspect-js/functions-have-names#info=devDependencies
[npm-badge-png]: https://nodei.co/npm/functions-have-names.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/functions-have-names.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/functions-have-names.svg
[downloads-url]: https://npm-stat.com/charts.html?package=functions-have-names
[codecov-image]: https://codecov.io/gh/inspect-js/functions-have-names/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/inspect-js/functions-have-names/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/inspect-js/functions-have-names
[actions-url]: https://github.com/inspect-js/functions-have-names/actions
