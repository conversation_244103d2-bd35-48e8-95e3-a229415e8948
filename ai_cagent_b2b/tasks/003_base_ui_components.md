# Task: Base UI Components

## Objective
Implement the base UI components that will be used throughout the application, focusing on reusability, accessibility, and consistent styling.

## Requirements

1. Create a component library of reusable UI elements
2. Ensure components follow accessibility best practices
3. Implement responsive design for all components
4. Support both light and dark themes
5. Support RTL layout for Arabic language

## Implementation Steps

### 1. Setup UI Component Structure

- Create the base UI component directory structure:
  ```
  src/components/ui/
  ├── Button/
  ├── Input/
  ├── Select/
  ├── Modal/
  ├── Dropdown/
  ├── Card/
  ├── Alert/
  ├── Badge/
  ├── Toggle/
  └── Table/
  ```

### 2. Implement Core UI Components

- Button Component (`src/components/ui/Button/index.tsx`):
  ```typescript
  // Primary, secondary, outline, text variants
  // Size variants: small, medium, large
  // Loading state
  // Icon support
  ```

- Input Component (`src/components/ui/Input/index.tsx`):
  ```typescript
  // Text, number, email, password types
  // Validation states
  // Label and helper text
  ```

- Select Component (`src/components/ui/Select/index.tsx`):
  ```typescript
  // Single and multi-select
  // Searchable option
  // Custom rendering of options
  ```

- Modal Component (`src/components/ui/Modal/index.tsx`):
  ```typescript
  // Header, body, footer structure
  // Close button
  // Backdrop click handling
  ```

- Dropdown Component (`src/components/ui/Dropdown/index.tsx`):
  ```typescript
  // Trigger element
  // Dropdown menu
  // Item selection
  ```

- Card Component (`src/components/ui/Card/index.tsx`):
  ```typescript
  // Header, body, footer structure
  // Variants: default, outlined, elevated
  ```

- Alert Component (`src/components/ui/Alert/index.tsx`):
  ```typescript
  // Info, success, warning, error variants
  // Dismissible option
  ```

- Badge Component (`src/components/ui/Badge/index.tsx`):
  ```typescript
  // Color variants
  // Size variants
  // Dot variant
  ```

- Toggle Component (`src/components/ui/Toggle/index.tsx`):
  ```typescript
  // On/off states
  // Size variants
  // Label support
  ```

- Table Component (`src/components/ui/Table/index.tsx`):
  ```typescript
  // Header, body, footer
  // Sortable columns
  // Pagination
  ```

### 3. Implement Theme and RTL Support

- Create theme utility functions (`src/utils/theme.ts`):
  ```typescript
  // Theme detection
  // Theme switching
  ```

- Create RTL utility functions (`src/utils/rtl.ts`):
  ```typescript
  // RTL detection
  // RTL-specific styles
  ```

### 4. Create Component Storybook (Optional)

- Set up Storybook for component documentation and testing
- Create stories for each component with various states and variants

## Deliverables

1. Complete set of base UI components
2. Components with theme support (light/dark)
3. Components with RTL support
4. Accessibility-compliant components
5. Component documentation (inline or Storybook)

## Dependencies

- Task 001: Project Setup
- Task 002: Core Architecture
- Tailwind CSS

## Estimated Time
8 hours