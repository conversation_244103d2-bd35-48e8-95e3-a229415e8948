// Common types used throughout the application

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  company?: Company;
  avatar?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Company {
  id: string;
  name: string;
  address: Address;
  contactInfo: ContactInfo;
  taxId?: string;
  isActive: boolean;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  country: string;
  postalCode: string;
}

export interface ContactInfo {
  phone: string;
  email: string;
  website?: string;
}

export type UserRole = 'admin' | 'manager' | 'buyer' | 'viewer';

export type Locale = 'en' | 'ar';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface Product {
  id: string;
  name: string;
  description: string;
  sku: string;
  price: number;
  currency: string;
  category: ProductCategory;
  images: string[];
  specifications: Record<string, any>;
  isActive: boolean;
  stock: number;
  rating?: number;
  reviewCount?: number;
  brand?: string;
  variants?: ProductVariant[];
  createdAt: string;
  updatedAt: string;
}

export interface ProductVariant {
  id: string;
  name: string;
  value: string;
  type: 'color' | 'size' | 'storage' | 'memory';
  price?: number; // Additional price for this variant
  stock?: number;
  image?: string;
}

export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  isActive: boolean;
}

export interface Order {
  id: string;
  orderNumber: string;
  customerId: string;
  customer: User;
  items: OrderItem[];
  status: OrderStatus;
  totalAmount: number;
  currency: string;
  shippingAddress: Address;
  billingAddress: Address;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  id: string;
  productId: string;
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
}

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'processing'
  | 'shipped'
  | 'delivered'
  | 'cancelled'
  | 'refunded';

export interface Contract {
  id: string;
  customerId: string;
  customer: User;
  name: string;
  description?: string;
  startDate: string;
  endDate: string;
  terms: ContractTerm[];
  status: ContractStatus;
  createdAt: string;
  updatedAt: string;
}

export interface ContractTerm {
  id: string;
  productId: string;
  product: Product;
  discountPercentage: number;
  minimumQuantity: number;
  maximumQuantity?: number;
}

export type ContractStatus = 'draft' | 'active' | 'expired' | 'terminated';

// UI Component Props
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
}
