'use client';

import { useEffect, useState } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import { Locale } from '@/types';

import { translateSync, formatCurrency, formatDate, formatNumber, pluralize } from './translate';

interface UseTranslationReturn {
  t: (key: string, variables?: Record<string, string | number>) => string;
  locale: Locale;
  isRTL: boolean;
  formatCurrency: (amount: number, currency: string) => string;
  formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions) => string;
  formatNumber: (number: number, options?: Intl.NumberFormatOptions) => string;
  pluralize: (count: number, singular: string, plural?: string) => string;
  loading: boolean;
}

/**
 * Custom hook for translations
 */
export function useTranslation(namespace: string = 'common'): UseTranslationReturn {
  const { locale, isRTL } = useLanguage();
  const [translations, setTranslations] = useState<Record<string, any>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const loadTranslations = async () => {
      setLoading(true);
      try {
        const translationModule = await import(`../../../public/locales/${locale}/${namespace}.json`);
        if (isMounted) {
          setTranslations(translationModule.default || translationModule);
        }
      } catch (error) {
        console.warn(`Failed to load translations for ${locale}/${namespace}:`, error);
        // Fallback to English
        if (locale !== 'en') {
          try {
            const fallbackModule = await import(`../../../public/locales/en/${namespace}.json`);
            if (isMounted) {
              setTranslations(fallbackModule.default || fallbackModule);
            }
          } catch (fallbackError) {
            console.error('Failed to load fallback translations:', fallbackError);
            if (isMounted) {
              setTranslations({});
            }
          }
        } else {
          if (isMounted) {
            setTranslations({});
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadTranslations();

    return () => {
      isMounted = false;
    };
  }, [locale, namespace]);

  const t = (key: string, variables?: Record<string, string | number>) => {
    return translateSync(key, translations, variables);
  };

  return {
    t,
    locale,
    isRTL,
    formatCurrency: (amount: number, currency: string) => formatCurrency(amount, currency, locale),
    formatDate: (date: Date | string, options?: Intl.DateTimeFormatOptions) => formatDate(date, locale, options),
    formatNumber: (number: number, options?: Intl.NumberFormatOptions) => formatNumber(number, locale, options),
    pluralize: (count: number, singular: string, plural?: string) => pluralize(count, singular, plural, locale),
    loading,
  };
}

/**
 * Hook for multiple namespaces
 */
export function useTranslations(namespaces: string[]): Record<string, UseTranslationReturn> {
  const results: Record<string, UseTranslationReturn> = {};
  
  namespaces.forEach(namespace => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    results[namespace] = useTranslation(namespace);
  });
  
  return results;
}
