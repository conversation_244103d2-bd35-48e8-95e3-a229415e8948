import { Locale } from '@/types';

// Translation cache to avoid repeated file loads
const translationCache: Record<string, Record<string, any>> = {};

/**
 * Load translation file for a specific locale and namespace
 */
async function loadTranslations(locale: Locale, namespace: string = 'common'): Promise<Record<string, any>> {
  const cacheKey = `${locale}-${namespace}`;
  
  if (translationCache[cacheKey]) {
    return translationCache[cacheKey];
  }

  try {
    const translations = await import(`../../../public/locales/${locale}/${namespace}.json`);
    const translationData = translations.default || translations;
    translationCache[cacheKey] = translationData;
    return translationData;
  } catch (error) {
    console.warn(`Failed to load translations for ${locale}/${namespace}:`, error);
    // Fallback to English if the requested locale fails
    if (locale !== 'en') {
      return loadTranslations('en', namespace);
    }
    return {};
  }
}

/**
 * Get a translation value by key path
 */
function getNestedValue(obj: Record<string, any>, path: string): string | undefined {
  const result = path.split('.').reduce((current: any, key) => {
    return current && current[key] !== undefined ? current[key] : undefined;
  }, obj);

  return typeof result === 'string' ? result : undefined;
}

/**
 * Interpolate variables in translation strings
 */
function interpolate(template: string, variables: Record<string, string | number> = {}): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return variables[key]?.toString() || match;
  });
}

/**
 * Main translation function
 */
export async function translate(
  key: string,
  locale: Locale,
  namespace: string = 'common',
  variables: Record<string, string | number> = {}
): Promise<string> {
  const translations = await loadTranslations(locale, namespace);
  const value = getNestedValue(translations, key);
  
  if (typeof value === 'string') {
    return interpolate(value, variables);
  }
  
  // Return the key if translation is not found
  return key;
}

/**
 * Synchronous translation function for use with pre-loaded translations
 */
export function translateSync(
  key: string,
  translations: Record<string, any>,
  variables: Record<string, string | number> = {}
): string {
  const value = getNestedValue(translations, key);
  
  if (typeof value === 'string') {
    return interpolate(value, variables);
  }
  
  return key;
}

/**
 * Pluralization function
 */
export function pluralize(
  count: number,
  singular: string,
  plural?: string,
  locale: Locale = 'en'
): string {
  // Simple pluralization rules
  if (locale === 'ar') {
    // Arabic pluralization rules (simplified)
    if (count === 0) return plural || singular;
    if (count === 1) return singular;
    if (count === 2) return plural || singular;
    if (count >= 3 && count <= 10) return plural || singular;
    return plural || singular;
  } else {
    // English pluralization rules
    return count === 1 ? singular : (plural || singular + 's');
  }
}

/**
 * Format numbers according to locale
 */
export function formatNumber(
  number: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(locale, options).format(number);
}

/**
 * Format currency according to locale
 */
export function formatCurrency(
  amount: number,
  currency: string,
  locale: Locale
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Format dates according to locale
 */
export function formatDate(
  date: Date | string,
  locale: Locale,
  options?: Intl.DateTimeFormatOptions
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  };
  
  return new Intl.DateTimeFormat(locale, options || defaultOptions).format(dateObj);
}

/**
 * Format relative time according to locale
 */
export function formatRelativeTime(
  date: Date | string,
  locale: Locale
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);
  
  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });
  
  if (diffInSeconds < 60) {
    return rtf.format(-diffInSeconds, 'second');
  } else if (diffInSeconds < 3600) {
    return rtf.format(-Math.floor(diffInSeconds / 60), 'minute');
  } else if (diffInSeconds < 86400) {
    return rtf.format(-Math.floor(diffInSeconds / 3600), 'hour');
  } else if (diffInSeconds < 2592000) {
    return rtf.format(-Math.floor(diffInSeconds / 86400), 'day');
  } else if (diffInSeconds < 31536000) {
    return rtf.format(-Math.floor(diffInSeconds / 2592000), 'month');
  } else {
    return rtf.format(-Math.floor(diffInSeconds / 31536000), 'year');
  }
}

/**
 * Get text direction for locale
 */
export function getTextDirection(locale: Locale): 'ltr' | 'rtl' {
  const rtlLanguages: Locale[] = ['ar'];
  return rtlLanguages.includes(locale) ? 'rtl' : 'ltr';
}

/**
 * Check if locale uses RTL
 */
export function isRTL(locale: Locale): boolean {
  return getTextDirection(locale) === 'rtl';
}
