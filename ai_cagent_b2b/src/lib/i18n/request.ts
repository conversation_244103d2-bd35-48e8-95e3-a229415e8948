import { getRequestConfig } from 'next-intl/server';

import { routing } from './config';

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!routing.locales.includes(locale as any)) {
    throw new Error(`Invalid locale: ${locale}`);
  }

  return {
    messages: (await import(`../../../public/locales/${locale}/common.json`)).default
  };
});
