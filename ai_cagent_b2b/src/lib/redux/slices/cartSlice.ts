import { createSlice, PayloadAction } from '@reduxjs/toolkit';

import { Product } from '@/types';

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
  selectedVariant?: {
    color?: string;
    size?: string;
    storage?: string;
  };
  addedAt: string;
}

interface CartState {
  items: CartItem[];
  isOpen: boolean;
  totalItems: number;
  totalAmount: number;
  currency: string;
  shippingCost: number;
  taxRate: number;
  discountCode?: string;
  discountAmount: number;
}

const initialState: CartState = {
  items: [],
  isOpen: false,
  totalItems: 0,
  totalAmount: 0,
  currency: 'USD',
  shippingCost: 0,
  taxRate: 0.08, // 8% tax
  discountAmount: 0,
};

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    addToCart: (state, action: PayloadAction<{
      product: Product;
      quantity?: number;
      variant?: CartItem['selectedVariant'];
    }>) => {
      const { product, quantity = 1, variant } = action.payload;
      
      // Check if item already exists with same variant
      const existingItemIndex = state.items.findIndex(item =>
        item.product.id === product.id &&
        JSON.stringify(item.selectedVariant) === JSON.stringify(variant)
      );

      if (existingItemIndex >= 0) {
        // Update quantity if item exists
        const existingItem = state.items[existingItemIndex];
        if (existingItem) {
          existingItem.quantity += quantity;
        }
      } else {
        // Add new item
        const newItem: CartItem = {
          id: `${product.id}-${Date.now()}`,
          product,
          quantity,
          selectedVariant: variant,
          addedAt: new Date().toISOString(),
        };
        state.items.push(newItem);
      }

      calculateTotals(state);
    },

    removeFromCart: (state, action: PayloadAction<string>) => {
      state.items = state.items.filter(item => item.id !== action.payload);
      calculateTotals(state);
    },

    updateQuantity: (state, action: PayloadAction<{
      itemId: string;
      quantity: number;
    }>) => {
      const { itemId, quantity } = action.payload;
      const item = state.items.find(item => item.id === itemId);
      
      if (item) {
        if (quantity <= 0) {
          state.items = state.items.filter(item => item.id !== itemId);
        } else {
          item.quantity = quantity;
        }
      }

      calculateTotals(state);
    },

    clearCart: (state) => {
      state.items = [];
      state.totalItems = 0;
      state.totalAmount = 0;
      state.discountAmount = 0;
      state.discountCode = undefined;
    },

    toggleCart: (state) => {
      state.isOpen = !state.isOpen;
    },

    openCart: (state) => {
      state.isOpen = true;
    },

    closeCart: (state) => {
      state.isOpen = false;
    },

    applyDiscountCode: (state, action: PayloadAction<string>) => {
      const code = action.payload.toUpperCase();
      
      // Mock discount codes
      const discountCodes: Record<string, number> = {
        'WELCOME10': 0.1, // 10% off
        'SAVE20': 0.2,    // 20% off
        'STUDENT15': 0.15, // 15% off
      };

      if (discountCodes[code]) {
        state.discountCode = code;
        state.discountAmount = state.totalAmount * discountCodes[code];
      }
    },

    removeDiscountCode: (state) => {
      state.discountCode = undefined;
      state.discountAmount = 0;
    },

    setShippingCost: (state, action: PayloadAction<number>) => {
      state.shippingCost = action.payload;
      calculateTotals(state);
    },
  },
});

// Helper function to calculate totals
function calculateTotals(state: CartState) {
  state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
  
  const subtotal = state.items.reduce(
    (total, item) => total + (item.product.price * item.quantity),
    0
  );
  
  const tax = subtotal * state.taxRate;
  state.totalAmount = subtotal + tax + state.shippingCost - state.discountAmount;
}

export const {
  addToCart,
  removeFromCart,
  updateQuantity,
  clearCart,
  toggleCart,
  openCart,
  closeCart,
  applyDiscountCode,
  removeDiscountCode,
  setShippingCost,
} = cartSlice.actions;

export default cartSlice.reducer;
