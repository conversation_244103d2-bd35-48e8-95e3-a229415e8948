import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { Order, OrderStatus } from '@/types';

interface OrdersState {
  items: Order[];
  currentOrder: Order | null;
  loading: boolean;
  error: string | null;
  filter: {
    status: OrderStatus | 'all';
    dateRange: {
      start: string | null;
      end: string | null;
    };
  };
}

const initialState: OrdersState = {
  items: [],
  currentOrder: null,
  loading: false,
  error: null,
  filter: {
    status: 'all',
    dateRange: {
      start: null,
      end: null,
    },
  },
};

// Async thunks for API calls
export const fetchOrders = createAsyncThunk(
  'orders/fetchOrders',
  async () => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockOrders: Order[] = [
      {
        id: '1',
        orderNumber: 'ORD-2024-001',
        customerId: 'user1',
        customer: {
          id: 'user1',
          email: '<EMAIL>',
          name: '<PERSON>',
          role: 'buyer',
          isActive: true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        items: [
          {
            id: '1',
            productId: '1',
            product: {
              id: '1',
              name: 'iPhone 15 Pro',
              description: 'Latest iPhone with A17 Pro chip',
              sku: 'IPH15PRO-128',
              price: 999,
              currency: 'USD',
              category: {
                id: 'smartphones',
                name: 'Smartphones',
                isActive: true,
              },
              images: ['/images/iphone-15-pro.jpg'],
              specifications: {},
              isActive: true,
              stock: 50,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            },
            quantity: 1,
            unitPrice: 999,
            totalPrice: 999,
          },
        ],
        status: 'confirmed',
        totalAmount: 999,
        currency: 'USD',
        shippingAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          country: 'USA',
          postalCode: '10001',
        },
        billingAddress: {
          street: '123 Main St',
          city: 'New York',
          state: 'NY',
          country: 'USA',
          postalCode: '10001',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
    
    return mockOrders;
  }
);

export const createOrder = createAsyncThunk(
  'orders/createOrder',
  async (orderData: Partial<Order>) => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const newOrder: Order = {
      id: Date.now().toString(),
      orderNumber: `ORD-2024-${String(Date.now()).slice(-3)}`,
      customerId: orderData.customerId || 'user1',
      customer: orderData.customer || {
        id: 'user1',
        email: '<EMAIL>',
        name: 'User',
        role: 'buyer',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      items: orderData.items || [],
      status: 'pending',
      totalAmount: orderData.totalAmount || 0,
      currency: orderData.currency || 'USD',
      shippingAddress: orderData.shippingAddress || {
        street: '',
        city: '',
        state: '',
        country: '',
        postalCode: '',
      },
      billingAddress: orderData.billingAddress || {
        street: '',
        city: '',
        state: '',
        country: '',
        postalCode: '',
      },
      notes: orderData.notes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    return newOrder;
  }
);

export const updateOrderStatus = createAsyncThunk(
  'orders/updateOrderStatus',
  async ({ orderId, status }: { orderId: string; status: OrderStatus }) => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    return { orderId, status };
  }
);

const ordersSlice = createSlice({
  name: 'orders',
  initialState,
  reducers: {
    setCurrentOrder: (state, action: PayloadAction<Order | null>) => {
      state.currentOrder = action.payload;
    },
    setStatusFilter: (state, action: PayloadAction<OrderStatus | 'all'>) => {
      state.filter.status = action.payload;
    },
    setDateRangeFilter: (state, action: PayloadAction<{
      start: string | null;
      end: string | null;
    }>) => {
      state.filter.dateRange = action.payload;
    },
    clearFilters: (state) => {
      state.filter = {
        status: 'all',
        dateRange: {
          start: null,
          end: null,
        },
      };
    },
    addOrderNote: (state, action: PayloadAction<{
      orderId: string;
      note: string;
    }>) => {
      const { orderId, note } = action.payload;
      const order = state.items.find(order => order.id === orderId);
      if (order) {
        order.notes = order.notes ? `${order.notes}\n${note}` : note;
        order.updatedAt = new Date().toISOString();
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch orders';
      })
      .addCase(createOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.items.unshift(action.payload);
        state.currentOrder = action.payload;
      })
      .addCase(createOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to create order';
      })
      .addCase(updateOrderStatus.fulfilled, (state, action) => {
        const { orderId, status } = action.payload;
        const order = state.items.find(order => order.id === orderId);
        if (order) {
          order.status = status;
          order.updatedAt = new Date().toISOString();
        }
        if (state.currentOrder && state.currentOrder.id === orderId) {
          state.currentOrder.status = status;
          state.currentOrder.updatedAt = new Date().toISOString();
        }
      });
  },
});

export const {
  setCurrentOrder,
  setStatusFilter,
  setDateRangeFilter,
  clearFilters,
  addOrderNote,
} = ordersSlice.actions;

export default ordersSlice.reducer;
