import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { User, Locale } from '@/types';

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: Locale;
  currency: string;
  notifications: {
    email: boolean;
    push: boolean;
    orderUpdates: boolean;
    promotions: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    showEmail: boolean;
    showPhone: boolean;
  };
}

interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
  preferences: UserPreferences;
  loading: boolean;
  error: string | null;
  loginAttempts: number;
  lastLoginAt: string | null;
}

const initialState: UserState = {
  currentUser: null,
  isAuthenticated: false,
  preferences: {
    theme: 'system',
    language: 'en',
    currency: 'USD',
    notifications: {
      email: true,
      push: true,
      orderUpdates: true,
      promotions: false,
    },
    privacy: {
      profileVisibility: 'private',
      showEmail: false,
      showPhone: false,
    },
  },
  loading: false,
  error: null,
  loginAttempts: 0,
  lastLoginAt: null,
};

// Async thunks for authentication
export const loginUser = createAsyncThunk(
  'user/login',
  async (credentials: { email: string; password: string }) => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock authentication logic
    if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
      const mockUser: User = {
        id: '1',
        email: credentials.email,
        name: 'Demo User',
        role: 'buyer',
        company: {
          id: 'company1',
          name: 'Demo Electronics Corp',
          address: {
            street: '123 Business Ave',
            city: 'Tech City',
            state: 'CA',
            country: 'USA',
            postalCode: '90210',
          },
          contactInfo: {
            phone: '******-0123',
            email: '<EMAIL>',
            website: 'https://demoelectronics.com',
          },
          taxId: 'TAX123456789',
          isActive: true,
        },
        avatar: '/images/avatar-placeholder.jpg',
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      return mockUser;
    } else {
      throw new Error('Invalid credentials');
    }
  }
);

export const logoutUser = createAsyncThunk(
  'user/logout',
  async () => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return true;
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (updates: Partial<User>) => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    return updates;
  }
);

export const updateUserPreferences = createAsyncThunk(
  'user/updatePreferences',
  async (preferences: Partial<UserPreferences>) => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return preferences;
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.preferences.theme = action.payload;
    },
    setLanguage: (state, action: PayloadAction<Locale>) => {
      state.preferences.language = action.payload;
    },
    setCurrency: (state, action: PayloadAction<string>) => {
      state.preferences.currency = action.payload;
    },
    updateNotificationSettings: (state, action: PayloadAction<Partial<UserPreferences['notifications']>>) => {
      state.preferences.notifications = {
        ...state.preferences.notifications,
        ...action.payload,
      };
    },
    updatePrivacySettings: (state, action: PayloadAction<Partial<UserPreferences['privacy']>>) => {
      state.preferences.privacy = {
        ...state.preferences.privacy,
        ...action.payload,
      };
    },
    clearError: (state) => {
      state.error = null;
    },
    incrementLoginAttempts: (state) => {
      state.loginAttempts += 1;
    },
    resetLoginAttempts: (state) => {
      state.loginAttempts = 0;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.loading = false;
        state.currentUser = action.payload;
        state.isAuthenticated = true;
        state.loginAttempts = 0;
        state.lastLoginAt = new Date().toISOString();
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Login failed';
        state.loginAttempts += 1;
      })
      .addCase(logoutUser.fulfilled, (state) => {
        state.currentUser = null;
        state.isAuthenticated = false;
        state.error = null;
        state.loginAttempts = 0;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        if (state.currentUser) {
          state.currentUser = {
            ...state.currentUser,
            ...action.payload,
            updatedAt: new Date().toISOString(),
          };
        }
      })
      .addCase(updateUserPreferences.fulfilled, (state, action) => {
        state.preferences = {
          ...state.preferences,
          ...action.payload,
        };
      });
  },
});

export const {
  setTheme,
  setLanguage,
  setCurrency,
  updateNotificationSettings,
  updatePrivacySettings,
  clearError,
  incrementLoginAttempts,
  resetLoginAttempts,
} = userSlice.actions;

export default userSlice.reducer;
