import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

import { Product, ProductCategory } from '@/types';

interface ProductsState {
  items: Product[];
  categories: ProductCategory[];
  filteredItems: Product[];
  selectedCategory: string | null;
  searchQuery: string;
  sortBy: 'name' | 'price' | 'rating' | 'newest';
  sortOrder: 'asc' | 'desc';
  priceRange: [number, number];
  loading: boolean;
  error: string | null;
  currentProduct: Product | null;
}

const initialState: ProductsState = {
  items: [],
  categories: [],
  filteredItems: [],
  selectedCategory: null,
  searchQuery: '',
  sortBy: 'name',
  sortOrder: 'asc',
  priceRange: [0, 10000],
  loading: false,
  error: null,
  currentProduct: null,
};

// Async thunks for API calls (will be replaced with real API later)
export const fetchProducts = createAsyncThunk(
  'products/fetchProducts',
  async () => {
    // Mock API call - will be replaced with real API
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const mockProducts: Product[] = [
      {
        id: '1',
        name: 'iPhone 15 Pro',
        description: 'Latest iPhone with A17 Pro chip and titanium design',
        sku: 'IPH15PRO-128',
        price: 999,
        currency: 'USD',
        category: {
          id: 'smartphones',
          name: 'Smartphones',
          isActive: true,
        },
        images: ['/images/iphone-15-pro.jpg'],
        specifications: {
          storage: '128GB',
          color: 'Natural Titanium',
          display: '6.1-inch Super Retina XDR',
          chip: 'A17 Pro',
        },
        isActive: true,
        stock: 50,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: 'MacBook Pro 14"',
        description: 'Powerful laptop with M3 chip for professionals',
        sku: 'MBP14-M3-512',
        price: 1999,
        currency: 'USD',
        category: {
          id: 'laptops',
          name: 'Laptops',
          isActive: true,
        },
        images: ['/images/macbook-pro-14.jpg'],
        specifications: {
          processor: 'Apple M3',
          memory: '16GB',
          storage: '512GB SSD',
          display: '14.2-inch Liquid Retina XDR',
        },
        isActive: true,
        stock: 25,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '3',
        name: 'iPad Air',
        description: 'Versatile tablet with M2 chip and Apple Pencil support',
        sku: 'IPAD-AIR-M2-256',
        price: 749,
        currency: 'USD',
        category: {
          id: 'tablets',
          name: 'Tablets',
          isActive: true,
        },
        images: ['/images/ipad-air.jpg'],
        specifications: {
          chip: 'Apple M2',
          storage: '256GB',
          display: '10.9-inch Liquid Retina',
          connectivity: 'Wi-Fi + Cellular',
        },
        isActive: true,
        stock: 40,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];
    
    return mockProducts;
  }
);

export const fetchCategories = createAsyncThunk(
  'products/fetchCategories',
  async () => {
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const mockCategories: ProductCategory[] = [
      { id: 'smartphones', name: 'Smartphones', isActive: true },
      { id: 'laptops', name: 'Laptops', isActive: true },
      { id: 'tablets', name: 'Tablets', isActive: true },
      { id: 'accessories', name: 'Accessories', isActive: true },
      { id: 'audio', name: 'Audio', isActive: true },
    ];
    
    return mockCategories;
  }
);

const productsSlice = createSlice({
  name: 'products',
  initialState,
  reducers: {
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
      filterProducts(state);
    },
    setSelectedCategory: (state, action: PayloadAction<string | null>) => {
      state.selectedCategory = action.payload;
      filterProducts(state);
    },
    setSortBy: (state, action: PayloadAction<'name' | 'price' | 'rating' | 'newest'>) => {
      state.sortBy = action.payload;
      sortProducts(state);
    },
    setSortOrder: (state, action: PayloadAction<'asc' | 'desc'>) => {
      state.sortOrder = action.payload;
      sortProducts(state);
    },
    setPriceRange: (state, action: PayloadAction<[number, number]>) => {
      state.priceRange = action.payload;
      filterProducts(state);
    },
    setCurrentProduct: (state, action: PayloadAction<Product | null>) => {
      state.currentProduct = action.payload;
    },
    clearFilters: (state) => {
      state.searchQuery = '';
      state.selectedCategory = null;
      state.priceRange = [0, 10000];
      state.sortBy = 'name';
      state.sortOrder = 'asc';
      state.filteredItems = state.items;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        state.filteredItems = action.payload;
      })
      .addCase(fetchProducts.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch products';
      })
      .addCase(fetchCategories.fulfilled, (state, action) => {
        state.categories = action.payload;
      });
  },
});

// Helper functions
function filterProducts(state: ProductsState) {
  let filtered = state.items;

  // Filter by search query
  if (state.searchQuery) {
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
      product.description.toLowerCase().includes(state.searchQuery.toLowerCase())
    );
  }

  // Filter by category
  if (state.selectedCategory) {
    filtered = filtered.filter(product => product.category.id === state.selectedCategory);
  }

  // Filter by price range
  filtered = filtered.filter(product =>
    product.price >= state.priceRange[0] && product.price <= state.priceRange[1]
  );

  state.filteredItems = filtered;
  sortProducts(state);
}

function sortProducts(state: ProductsState) {
  state.filteredItems.sort((a, b) => {
    let comparison = 0;
    
    switch (state.sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name);
        break;
      case 'price':
        comparison = a.price - b.price;
        break;
      case 'newest':
        comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        break;
      default:
        comparison = 0;
    }
    
    return state.sortOrder === 'desc' ? -comparison : comparison;
  });
}

export const {
  setSearchQuery,
  setSelectedCategory,
  setSortBy,
  setSortOrder,
  setPriceRange,
  setCurrentProduct,
  clearFilters,
} = productsSlice.actions;

export default productsSlice.reducer;
