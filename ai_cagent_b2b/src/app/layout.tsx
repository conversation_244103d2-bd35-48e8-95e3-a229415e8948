import type { Metada<PERSON> } from 'next';
import { Inter } from 'next/font/google';

import { Providers } from '@/components/Providers';

import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'B2B Portal',
  description: 'Next.js B2B E-commerce Portal',
  keywords: ['B2B', 'E-commerce', 'Portal', 'Next.js'],
  authors: [{ name: 'B2B Portal Team' }],
  robots: 'index, follow',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          <div id="root">
            {children}
          </div>
        </Providers>
      </body>
    </html>
  );
}
