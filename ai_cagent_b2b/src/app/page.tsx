'use client';

import Link from 'next/link';
import { useEffect } from 'react';

import { fetchProducts } from '@/lib/redux/slices/productsSlice';
import { useAppDispatch, useAppSelector } from '@/lib/redux/store';

export default function HomePage() {
  const dispatch = useAppDispatch();
  const { items: products, loading } = useAppSelector(state => state.products);
  const { totalItems } = useAppSelector(state => state.cart);

  useEffect(() => {
    dispatch(fetchProducts());
  }, [dispatch]);
  return (
    <main className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-secondary-900 mb-6">
            Welcome to ElectroShop
          </h1>
          <p className="text-xl text-secondary-600 mb-8 max-w-2xl mx-auto">
            Your premier destination for cutting-edge electronics and technology.
            Discover the latest smartphones, laptops, tablets, and accessories.
          </p>

          {totalItems > 0 && (
            <div className="mb-6 p-4 bg-primary-50 rounded-lg inline-block">
              <p className="text-primary-700 font-medium">
                🛒 You have {totalItems} item{totalItems !== 1 ? 's' : ''} in your cart
              </p>
            </div>
          )}
          
          {/* Featured Products Section */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
              <p className="mt-4 text-secondary-600">Loading featured products...</p>
            </div>
          ) : products.length > 0 ? (
            <div className="mb-12">
              <h2 className="text-2xl font-bold text-secondary-900 mb-6">Featured Products</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                {products.slice(0, 3).map((product) => (
                  <div key={product.id} className="card hover:shadow-lg transition-shadow">
                    <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                      <span className="text-4xl">📱</span>
                    </div>
                    <h3 className="font-semibold text-secondary-900 mb-2">{product.name}</h3>
                    <p className="text-secondary-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-bold text-primary-600">
                        ${product.price.toLocaleString()}
                      </span>
                      <Link href="/products" className="btn-primary text-sm px-3 py-1">
                        View Details
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : null}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-12 max-w-6xl mx-auto">
            <div className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                📱 Latest Smartphones
              </h3>
              <p className="text-secondary-600 mb-4">
                Discover the newest smartphones with cutting-edge technology and innovative features.
              </p>
              <Link href="/products" className="btn-primary inline-block">
                Shop Smartphones
              </Link>
            </div>
            
            <div className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                💻 Powerful Laptops
              </h3>
              <p className="text-secondary-600 mb-4">
                High-performance laptops for work, gaming, and creative professionals.
              </p>
              <Link href="/products" className="btn-primary inline-block">
                Shop Laptops
              </Link>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                📱 Tablets & iPads
              </h3>
              <p className="text-secondary-600 mb-4">
                Versatile tablets perfect for productivity, entertainment, and creativity.
              </p>
              <Link href="/products" className="btn-primary inline-block">
                Shop Tablets
              </Link>
            </div>
            
            <div className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                🎧 Audio & Accessories
              </h3>
              <p className="text-secondary-600 mb-4">
                Premium headphones, speakers, and tech accessories for every need.
              </p>
              <Link href="/products" className="btn-primary inline-block">
                Shop Audio
              </Link>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                📋 Order Tracking
              </h3>
              <p className="text-secondary-600 mb-4">
                Track your orders and manage your purchase history with ease.
              </p>
              <Link href="/orders" className="btn-primary inline-block">
                View Orders
              </Link>
            </div>

            <div className="card">
              <h3 className="text-lg font-semibold text-secondary-900 mb-3">
                👤 My Account
              </h3>
              <p className="text-secondary-600 mb-4">
                Manage your profile, preferences, and account settings.
              </p>
              <Link href="/profile" className="btn-primary inline-block">
                My Account
              </Link>
            </div>
          </div>
          
          <div className="mt-16 p-6 bg-white rounded-lg shadow-sm border border-secondary-200 max-w-4xl mx-auto">
            <h2 className="text-2xl font-semibold text-secondary-900 mb-4">
              Platform Features
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
              <div>
                <h4 className="font-medium text-secondary-900 mb-2">✅ Available Now</h4>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• Modern React Architecture</li>
                  <li>• Redux State Management</li>
                  <li>• TypeScript Integration</li>
                  <li>• Responsive Design</li>
                  <li>• Theme & Language Support</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-secondary-900 mb-2">🚧 Coming Soon</h4>
                <ul className="text-sm text-secondary-600 space-y-1">
                  <li>• Advanced Product Catalog</li>
                  <li>• Shopping Cart & Checkout</li>
                  <li>• User Authentication</li>
                  <li>• Order Management</li>
                  <li>• Analytics Dashboard</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
