'use client';

import React, { useEffect, useState } from 'react';

import { <PERSON><PERSON>, Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle, Input, Badge, Alert, AlertDescription, AlertTitle, AlertIcons } from '@/components/ui';
import { useAppDispatch, useAppSelector } from '@/lib/redux/store';
import { fetchProducts, setSearchQuery } from '@/lib/redux/slices/productsSlice';
import { addToCart } from '@/lib/redux/slices/cartSlice';

export default function ProductsPage() {
  const dispatch = useAppDispatch();
  const { filteredItems: products, loading, searchQuery } = useAppSelector(state => state.products);
  const [showAlert, setShowAlert] = useState(false);

  useEffect(() => {
    dispatch(fetchProducts());
  }, [dispatch]);

  const handleAddToCart = (product: any) => {
    dispatch(addToCart({ product }));
    setShow<PERSON><PERSON><PERSON>(true);
    setTimeout(() => setShowAlert(false), 3000);
  };

  const handleSearch = (value: string) => {
    dispatch(setSearchQuery(value));
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Product Catalog</h1>
          <p className="text-gray-600 mb-6">
            Discover our latest collection of premium electronics and technology products.
          </p>

          {showAlert && (
            <Alert variant="success" dismissible onDismiss={() => setShowAlert(false)} icon={AlertIcons.success} className="mb-6">
              <AlertTitle>Product Added!</AlertTitle>
              <AlertDescription>
                The product has been successfully added to your cart.
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="sm:max-w-sm"
              leftIcon={
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              }
            />
            <Button variant="outline">
              Filter
            </Button>
            <Button variant="outline">
              Sort
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading products...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map((product) => (
              <Card key={product.id} hover className="h-full flex flex-col">
                <CardHeader>
                  <div className="aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center">
                    <span className="text-6xl">📱</span>
                  </div>
                  <div className="flex justify-between items-start mb-2">
                    <CardTitle className="text-lg">{product.name}</CardTitle>
                    <Badge variant="primary">{product.category.name}</Badge>
                  </div>
                  <CardDescription className="line-clamp-2">
                    {product.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="flex-1">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">SKU:</span>
                      <span className="text-sm font-mono">{product.sku}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-500">Stock:</span>
                      <Badge variant={product.stock > 10 ? 'success' : product.stock > 0 ? 'warning' : 'error'}>
                        {product.stock > 0 ? `${product.stock} available` : 'Out of stock'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>

                <CardFooter className="flex justify-between items-center">
                  <div className="text-2xl font-bold text-primary-600">
                    ${product.price.toLocaleString()}
                  </div>
                  <Button
                    onClick={() => handleAddToCart(product)}
                    disabled={product.stock === 0}
                    size="sm"
                  >
                    {product.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        )}

        {!loading && products.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}
