'use client';

import React, { useState } from 'react';

import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Input,
  Badge,
  Alert,
  AlertDescription,
  AlertTitle,
  AlertIcons,
  Modal,
  ModalHeader,
  ModalTitle,
  ModalContent,
  ModalFooter,
  Toggle,
  Select,
  Dropdown
} from '@/components/ui';

export default function DashboardPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [toggleValue, setToggleValue] = useState(false);
  const [selectValue, setSelectValue] = useState('');

  const selectOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  const dropdownItems = [
    { key: 'edit', label: 'Edit', icon: '✏️' },
    { key: 'duplicate', label: 'Duplicate', icon: '📋' },
    { key: 'delete', label: 'Delete', icon: '🗑️', danger: true },
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">UI Components Dashboard</h1>
          <p className="text-gray-600">
            Showcase of all available UI components in the ElectroShop design system.
          </p>
        </div>

        {/* Alerts Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Alerts</h2>
          <div className="space-y-4">
            <Alert variant="info" icon={AlertIcons.info}>
              <AlertTitle>Information</AlertTitle>
              <AlertDescription>This is an informational alert message.</AlertDescription>
            </Alert>
            <Alert variant="success" icon={AlertIcons.success}>
              <AlertTitle>Success</AlertTitle>
              <AlertDescription>Your action was completed successfully!</AlertDescription>
            </Alert>
            <Alert variant="warning" icon={AlertIcons.warning}>
              <AlertTitle>Warning</AlertTitle>
              <AlertDescription>Please review your settings before proceeding.</AlertDescription>
            </Alert>
            <Alert variant="error" icon={AlertIcons.error} dismissible>
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>Something went wrong. Please try again.</AlertDescription>
            </Alert>
          </div>
        </div>

        {/* Buttons Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Buttons</h2>
          <div className="flex flex-wrap gap-4">
            <Button variant="primary">Primary</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="danger">Danger</Button>
            <Button variant="success">Success</Button>
            <Button loading>Loading</Button>
            <Button disabled>Disabled</Button>
            <Button size="sm">Small</Button>
            <Button size="lg">Large</Button>
            <Button leftIcon="🚀">With Icon</Button>
          </div>
        </div>

        {/* Form Components Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Form Components</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Input Components</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Input label="Email" type="email" placeholder="Enter your email" />
                <Input
                  label="Password"
                  type="password"
                  placeholder="Enter password"
                  helperText="Must be at least 8 characters"
                />
                <Input
                  label="Search"
                  placeholder="Search products..."
                  leftIcon="🔍"
                />
                <Input
                  label="Amount"
                  type="number"
                  placeholder="0.00"
                  rightIcon="💰"
                />
                <Input
                  label="Error Example"
                  error="This field is required"
                  placeholder="Required field"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Select & Toggle</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Select
                  label="Choose Option"
                  options={selectOptions}
                  value={selectValue}
                  onChange={setSelectValue}
                  placeholder="Select an option..."
                />
                <Select
                  label="Searchable Select"
                  options={selectOptions}
                  searchable
                  placeholder="Search options..."
                />
                <Toggle
                  checked={toggleValue}
                  onChange={setToggleValue}
                  label="Enable notifications"
                  description="Receive email notifications for updates"
                />
                <Toggle
                  checked={true}
                  label="Premium features"
                  variant="success"
                />
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Cards & Badges Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Cards & Badges</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card hover>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <CardTitle>Product Card</CardTitle>
                  <Badge variant="success">In Stock</Badge>
                </div>
                <CardDescription>
                  This is a sample product card with hover effects.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary-600">$299.99</div>
              </CardContent>
              <CardFooter>
                <Button fullWidth>Add to Cart</Button>
              </CardFooter>
            </Card>

            <Card variant="outlined">
              <CardHeader>
                <CardTitle>Outlined Card</CardTitle>
                <CardDescription>
                  Card with outlined variant styling.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-2 flex-wrap">
                  <Badge variant="primary">Primary</Badge>
                  <Badge variant="secondary">Secondary</Badge>
                  <Badge variant="success">Success</Badge>
                  <Badge variant="warning">Warning</Badge>
                  <Badge variant="error">Error</Badge>
                </div>
              </CardContent>
            </Card>

            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Elevated Card</CardTitle>
                <CardDescription>
                  Card with elevated shadow styling.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge size="sm" dot>Small Badge</Badge>
                  <Badge size="md" dot>Medium Badge</Badge>
                  <Badge size="lg" dot>Large Badge</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Interactive Components */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Interactive Components</h2>
          <div className="flex gap-4">
            <Button onClick={() => setIsModalOpen(true)}>Open Modal</Button>
            <Dropdown
              trigger={<Button variant="outline">Dropdown Menu</Button>}
              items={dropdownItems}
            />
          </div>
        </div>

        {/* Modal */}
        <Modal isOpen={isModalOpen} onClose={() => setIsModalOpen(false)}>
          <ModalHeader>
            <ModalTitle>Sample Modal</ModalTitle>
          </ModalHeader>
          <ModalContent>
            <p className="text-gray-600">
              This is a sample modal dialog. You can put any content here including forms,
              images, or other components.
            </p>
          </ModalContent>
          <ModalFooter>
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsModalOpen(false)}>
              Confirm
            </Button>
          </ModalFooter>
        </Modal>
      </div>
    </div>
  );
}
