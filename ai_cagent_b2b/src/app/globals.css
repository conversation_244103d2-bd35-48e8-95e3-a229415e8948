@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for theming */
:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* RTL-specific styles */
[dir="rtl"] .rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

[dir="rtl"] .rtl\:divide-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-x-reverse: 1;
}

/* RTL text alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL margins and padding */
[dir="rtl"] .ml-auto {
  margin-left: unset;
  margin-right: auto;
}

[dir="rtl"] .mr-auto {
  margin-right: unset;
  margin-left: auto;
}

/* RTL borders */
[dir="rtl"] .border-l {
  border-left: none;
  border-right: 1px solid;
}

[dir="rtl"] .border-r {
  border-right: none;
  border-left: 1px solid;
}

/* RTL positioning */
[dir="rtl"] .left-0 {
  left: unset;
  right: 0;
}

[dir="rtl"] .right-0 {
  right: unset;
  left: 0;
}

/* RTL transforms */
[dir="rtl"] .translate-x-full {
  transform: translateX(-100%);
}

[dir="rtl"] .-translate-x-full {
  transform: translateX(100%);
}

/* Custom utility classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 transition-colors;
  }
  
  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 px-4 py-2 rounded-lg font-medium hover:bg-secondary-200 focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2 transition-colors;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-secondary-200 p-6;
  }
}
