/**
 * RTL (Right-to-Left) utility functions for Arabic language support
 */

import { Locale } from '@/types';

/**
 * Languages that use RTL text direction
 */
export const RTL_LANGUAGES: Locale[] = ['ar'];

/**
 * Check if a locale uses RTL text direction
 */
export function isRTLLocale(locale: Locale): boolean {
  return RTL_LANGUAGES.includes(locale);
}

/**
 * Get text direction for a locale
 */
export function getTextDirection(locale: Locale): 'ltr' | 'rtl' {
  return isRTLLocale(locale) ? 'rtl' : 'ltr';
}

/**
 * Apply RTL direction to document
 */
export function applyTextDirection(locale: Locale): void {
  if (typeof document === 'undefined') return;
  
  const direction = getTextDirection(locale);
  document.documentElement.dir = direction;
  document.documentElement.lang = locale;
}

/**
 * Get RTL-aware positioning classes
 */
export function getRTLClasses(isRTL: boolean) {
  return {
    // Margin classes
    ml: isRTL ? 'mr' : 'ml',
    mr: isRTL ? 'ml' : 'mr',
    // Padding classes
    pl: isRTL ? 'pr' : 'pl',
    pr: isRTL ? 'pl' : 'pr',
    // Text alignment
    textLeft: isRTL ? 'text-right' : 'text-left',
    textRight: isRTL ? 'text-left' : 'text-right',
    // Float classes
    floatLeft: isRTL ? 'float-right' : 'float-left',
    floatRight: isRTL ? 'float-left' : 'float-right',
    // Border radius
    roundedL: isRTL ? 'rounded-r' : 'rounded-l',
    roundedR: isRTL ? 'rounded-l' : 'rounded-r',
    // Transform
    translateX: (value: string) => isRTL ? `-translate-x-${value}` : `translate-x-${value}`,
  };
}

/**
 * Get RTL-aware flex direction classes
 */
export function getFlexDirection(isRTL: boolean, direction: 'row' | 'col' = 'row') {
  if (direction === 'col') return 'flex-col';
  return isRTL ? 'flex-row-reverse' : 'flex-row';
}

/**
 * Convert logical properties to physical properties based on direction
 */
export function getLogicalProperties(isRTL: boolean) {
  return {
    inlineStart: isRTL ? 'right' : 'left',
    inlineEnd: isRTL ? 'left' : 'right',
    blockStart: 'top',
    blockEnd: 'bottom',
  };
}

/**
 * Get RTL-aware icon rotation classes
 */
export function getIconRotation(isRTL: boolean, icon: 'arrow' | 'chevron' = 'arrow') {
  if (!isRTL) return '';
  
  switch (icon) {
    case 'arrow':
    case 'chevron':
      return 'scale-x-[-1]';
    default:
      return '';
  }
}

/**
 * Format numbers for RTL locales
 */
export function formatNumber(
  number: number,
  locale: Locale,
  options?: Intl.NumberFormatOptions
): string {
  return new Intl.NumberFormat(locale, options).format(number);
}

/**
 * Format currency for RTL locales
 */
export function formatCurrency(
  amount: number,
  currency: string,
  locale: Locale
): string {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
}

/**
 * Get appropriate font family for locale
 */
export function getFontFamily(locale: Locale): string {
  switch (locale) {
    case 'ar':
      return 'font-arabic'; // You would define this in your Tailwind config
    case 'en':
    default:
      return 'font-sans';
  }
}
