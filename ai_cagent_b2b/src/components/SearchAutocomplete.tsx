'use client';

import React, { useState, useRef, useEffect } from 'react';

import { Input } from '@/components/ui';
import { useAppSelector } from '@/lib/redux/store';

interface SearchResult {
  id: string;
  title: string;
  type: 'product' | 'category';
  image?: string;
  price?: number;
}

export function SearchAutocomplete() {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  
  const { items: products } = useAppSelector(state => state.products);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (query.length > 1) {
      // Filter products based on search query
      const filteredProducts = products
        .filter(product => 
          product.name.toLowerCase().includes(query.toLowerCase()) ||
          product.description.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, 5)
        .map(product => ({
          id: product.id,
          title: product.name,
          type: 'product' as const,
          price: product.price,
        }));

      setResults(filteredProducts);
      setIsOpen(filteredProducts.length > 0);
    } else {
      setResults([]);
      setIsOpen(false);
    }
  }, [query, products]);

  const handleSearch = (value: string) => {
    setQuery(value);
  };

  const handleResultClick = (result: SearchResult) => {
    setQuery(result.title);
    setIsOpen(false);
    // Navigate to product or category page
    if (result.type === 'product') {
      window.location.href = `/products/${result.id}`;
    }
  };

  return (
    <div className="relative w-full max-w-md" ref={searchRef}>
      <Input
        placeholder="Search products..."
        value={query}
        onChange={(e) => handleSearch(e.target.value)}
        leftIcon={
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
        }
        className="pr-10"
      />
      
      {query && (
        <button
          onClick={() => {
            setQuery('');
            setIsOpen(false);
          }}
          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      )}

      {isOpen && results.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-secondary-300 rounded-lg shadow-lg max-h-80 overflow-auto">
          <div className="py-2">
            {results.map((result) => (
              <button
                key={result.id}
                onClick={() => handleResultClick(result)}
                className="w-full px-4 py-3 text-left hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-secondary-100 rounded flex items-center justify-center">
                      {result.type === 'product' ? '📱' : '📁'}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-secondary-900">
                        {result.title}
                      </div>
                      <div className="text-xs text-secondary-500 capitalize">
                        {result.type}
                      </div>
                    </div>
                  </div>
                  {result.price && (
                    <div className="text-sm font-medium text-primary-600">
                      ${result.price.toLocaleString()}
                    </div>
                  )}
                </div>
              </button>
            ))}
          </div>
          
          {query && (
            <div className="border-t border-secondary-200 px-4 py-2">
              <button
                onClick={() => {
                  // Navigate to search results page
                  window.location.href = `/products?search=${encodeURIComponent(query)}`;
                  setIsOpen(false);
                }}
                className="text-sm text-primary-600 hover:text-primary-700"
              >
                Search for &quot;{query}&quot;
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
