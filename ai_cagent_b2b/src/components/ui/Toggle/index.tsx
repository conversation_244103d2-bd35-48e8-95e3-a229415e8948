import { cva, type VariantProps } from 'class-variance-authority';
import React from 'react';

import { cn } from '@/utils';

const toggleVariants = cva(
  'peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      size: {
        sm: 'h-4 w-7',
        md: 'h-5 w-9',
        lg: 'h-6 w-11',
      },
      variant: {
        default: 'data-[state=checked]:bg-primary-600 data-[state=unchecked]:bg-secondary-200',
        success: 'data-[state=checked]:bg-success-600 data-[state=unchecked]:bg-secondary-200',
        warning: 'data-[state=checked]:bg-warning-600 data-[state=unchecked]:bg-secondary-200',
        error: 'data-[state=checked]:bg-error-600 data-[state=unchecked]:bg-secondary-200',
      },
    },
    defaultVariants: {
      size: 'md',
      variant: 'default',
    },
  }
);

const thumbVariants = cva(
  'pointer-events-none block rounded-full bg-white shadow-lg ring-0 transition-transform',
  {
    variants: {
      size: {
        sm: 'h-3 w-3 data-[state=checked]:translate-x-3 data-[state=unchecked]:translate-x-0',
        md: 'h-4 w-4 data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0',
        lg: 'h-5 w-5 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  }
);

export interface ToggleProps
  extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'onChange'>,
    VariantProps<typeof toggleVariants> {
  checked?: boolean;
  onChange?: (checked: boolean) => void;
  label?: string;
  description?: string;
}

const Toggle = React.forwardRef<HTMLButtonElement, ToggleProps>(
  ({ className, size, variant, checked = false, onChange, label, description, disabled, ...props }, ref) => {
    const handleClick = () => {
      if (!disabled && onChange) {
        onChange(!checked);
      }
    };

    return (
      <div className="flex items-center space-x-3">
        <button
          type="button"
          role="switch"
          aria-checked={checked}
          data-state={checked ? 'checked' : 'unchecked'}
          onClick={handleClick}
          className={cn(toggleVariants({ size, variant }), className)}
          disabled={disabled}
          ref={ref}
          {...props}
        >
          <span
            data-state={checked ? 'checked' : 'unchecked'}
            className={cn(thumbVariants({ size }))}
          />
        </button>
        
        {(label || description) && (
          <div className="flex flex-col">
            {label && (
              <label className="text-sm font-medium text-secondary-900 cursor-pointer" onClick={handleClick}>
                {label}
              </label>
            )}
            {description && (
              <p className="text-xs text-secondary-600">{description}</p>
            )}
          </div>
        )}
      </div>
    );
  }
);

Toggle.displayName = 'Toggle';

export { Toggle, toggleVariants };
