'use client';

import React, { useState, useRef, useEffect } from 'react';

import { cn } from '@/utils';

export interface DropdownItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  danger?: boolean;
  onClick?: () => void;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  align?: 'left' | 'right';
  className?: string;
  menuClassName?: string;
}

const Dropdown = React.forwardRef<HTMLDivElement, DropdownProps>(
  ({ trigger, items, align = 'left', className, menuClassName }, _ref) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
          setIsOpen(false);
        }
      };

      if (isOpen) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, [isOpen]);

    const handleItemClick = (item: DropdownItem) => {
      if (!item.disabled && item.onClick) {
        item.onClick();
      }
      setIsOpen(false);
    };

    return (
      <div className={cn('relative inline-block', className)} ref={dropdownRef}>
        <div onClick={() => setIsOpen(!isOpen)} className="cursor-pointer">
          {trigger}
        </div>

        {isOpen && (
          <div
            className={cn(
              'absolute z-50 mt-1 min-w-48 bg-white border border-secondary-300 rounded-lg shadow-lg py-1',
              align === 'right' ? 'right-0' : 'left-0',
              menuClassName
            )}
          >
            {items.map((item) => (
              <button
                key={item.key}
                type="button"
                className={cn(
                  'w-full px-3 py-2 text-left text-sm flex items-center space-x-2 hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none',
                  item.disabled && 'opacity-50 cursor-not-allowed',
                  item.danger && 'text-error-600 hover:bg-error-50 focus:bg-error-50'
                )}
                onClick={() => handleItemClick(item)}
                disabled={item.disabled}
              >
                {item.icon && <span className="flex-shrink-0">{item.icon}</span>}
                <span>{item.label}</span>
              </button>
            ))}
          </div>
        )}
      </div>
    );
  }
);

Dropdown.displayName = 'Dropdown';

export { Dropdown };
