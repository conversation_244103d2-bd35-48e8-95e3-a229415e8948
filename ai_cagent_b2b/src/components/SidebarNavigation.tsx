'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React, { useState } from 'react';

import { Button, Badge } from '@/components/ui';
import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/utils';

interface NavigationItem {
  id: string;
  label: string;
  href: string;
  icon: string;
  badge?: string;
  children?: NavigationItem[];
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    href: '/dashboard',
    icon: '📊',
  },
  {
    id: 'products',
    label: 'Products',
    href: '/products',
    icon: '📱',
    children: [
      { id: 'all-products', label: 'All Products', href: '/products', icon: '📦' },
      { id: 'smartphones', label: 'Smartphones', href: '/products?category=smartphones', icon: '📱' },
      { id: 'laptops', label: 'Laptops', href: '/products?category=laptops', icon: '💻' },
      { id: 'tablets', label: 'Tablets', href: '/products?category=tablets', icon: '📱' },
      { id: 'accessories', label: 'Accessories', href: '/products?category=accessories', icon: '🎧' },
    ],
  },
  {
    id: 'orders',
    label: 'Orders',
    href: '/orders',
    icon: '📋',
    badge: '3',
    children: [
      { id: 'all-orders', label: 'All Orders', href: '/orders', icon: '📋' },
      { id: 'pending', label: 'Pending', href: '/orders?status=pending', icon: '⏳' },
      { id: 'processing', label: 'Processing', href: '/orders?status=processing', icon: '⚙️' },
      { id: 'shipped', label: 'Shipped', href: '/orders?status=shipped', icon: '🚚' },
      { id: 'delivered', label: 'Delivered', href: '/orders?status=delivered', icon: '✅' },
    ],
  },
  {
    id: 'contracts',
    label: 'Contracts',
    href: '/contracts',
    icon: '📄',
  },
  {
    id: 'profile',
    label: 'My Account',
    href: '/profile',
    icon: '👤',
    children: [
      { id: 'profile-info', label: 'Profile Info', href: '/profile', icon: '👤' },
      { id: 'company', label: 'Company Details', href: '/profile/company', icon: '🏢' },
      { id: 'preferences', label: 'Preferences', href: '/profile/preferences', icon: '⚙️' },
      { id: 'security', label: 'Security', href: '/profile/security', icon: '🔒' },
    ],
  },
];

interface SidebarNavigationProps {
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function SidebarNavigation({ isCollapsed = false, onToggleCollapse }: SidebarNavigationProps) {
  const pathname = usePathname();
  const { isRTL } = useLanguage();
  const [expandedItems, setExpandedItems] = useState<string[]>(['products', 'orders']);

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (href: string) => {
    if (href === '/') return pathname === '/';
    return pathname.startsWith(href);
  };

  const renderNavigationItem = (item: NavigationItem, level = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = isActive(item.href);

    return (
      <div key={item.id}>
        <div className="flex items-center">
          <Link
            href={item.href}
            className={cn(
              'flex items-center flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors',
              level > 0 && 'ml-6',
              active
                ? 'bg-primary-100 text-primary-700'
                : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'
            )}
          >
            <span className="flex-shrink-0">{item.icon}</span>
            {!isCollapsed && (
              <>
                <span className={cn('ml-3', isRTL && 'mr-3 ml-0')}>{item.label}</span>
                {item.badge && (
                  <Badge variant="primary" size="sm" className="ml-auto">
                    {item.badge}
                  </Badge>
                )}
              </>
            )}
          </Link>
          
          {hasChildren && !isCollapsed && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => toggleExpanded(item.id)}
              className="p-1 ml-1"
            >
              <svg
                className={cn(
                  'h-4 w-4 transition-transform',
                  isExpanded && 'rotate-90'
                )}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Button>
          )}
        </div>

        {hasChildren && !isCollapsed && isExpanded && item.children && (
          <div className="mt-1 space-y-1">
            {item.children.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn(
      'bg-white border-r border-secondary-200 transition-all duration-300',
      isCollapsed ? 'w-16' : 'w-64'
    )}>
      <div className="p-4">
        {/* Collapse Toggle */}
        <div className="flex items-center justify-between mb-6">
          {!isCollapsed && (
            <h2 className="text-lg font-semibold text-secondary-900">Navigation</h2>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            className="p-1"
          >
            <svg
              className={cn(
                'h-4 w-4 transition-transform',
                isCollapsed && 'rotate-180'
              )}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
            </svg>
          </Button>
        </div>

        {/* Navigation Items */}
        <nav className="space-y-2">
          {navigationItems.map(item => renderNavigationItem(item))}
        </nav>

        {/* Quick Actions */}
        {!isCollapsed && (
          <div className="mt-8 pt-6 border-t border-secondary-200">
            <h3 className="text-sm font-medium text-secondary-500 mb-3">Quick Actions</h3>
            <div className="space-y-2">
              <Button variant="outline" size="sm" fullWidth className="justify-start">
                <span className="mr-2">➕</span>
                New Order
              </Button>
              <Button variant="outline" size="sm" fullWidth className="justify-start">
                <span className="mr-2">📞</span>
                Contact Support
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
