'use client';

import React from 'react';

import { Button, Dropdown } from '@/components/ui';
import { useLanguage } from '@/contexts/LanguageContext';
import { Locale } from '@/types';

const languages = [
  { code: 'en' as Locale, name: 'English', flag: '🇺🇸' },
  { code: 'ar' as Locale, name: 'العربية', flag: '🇸🇦' },
];

export function LanguageToggle() {
  const { locale, setLocale } = useLanguage();

  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  if (!currentLanguage) {
    return null;
  }
  
  const dropdownItems = languages.map(lang => ({
    key: lang.code,
    label: `${lang.flag} ${lang.name}`,
    onClick: () => setLocale(lang.code),
  }));

  return (
    <Dropdown
      trigger={
        <Button variant="ghost" size="sm" className="gap-2">
          <span>{currentLanguage.flag}</span>
          <span className="hidden sm:inline">{currentLanguage.name}</span>
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </Button>
      }
      items={dropdownItems}
      align="right"
    />
  );
}
