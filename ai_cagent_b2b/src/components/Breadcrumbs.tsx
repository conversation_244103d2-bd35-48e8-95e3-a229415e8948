'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/utils';

interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: string;
}

const routeLabels: Record<string, string> = {
  '': 'Home',
  'dashboard': 'Dashboard',
  'products': 'Products',
  'orders': 'Orders',
  'contracts': 'Contracts',
  'profile': 'Profile',
  'settings': 'Settings',
  'help': 'Help',
};

const routeIcons: Record<string, string> = {
  '': '🏠',
  'dashboard': '📊',
  'products': '📱',
  'orders': '📋',
  'contracts': '📄',
  'profile': '👤',
  'settings': '⚙️',
  'help': '❓',
};

export function Breadcrumbs() {
  const pathname = usePathname();
  const { isRTL } = useLanguage();

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Always include home
    breadcrumbs.push({
      label: routeLabels[''] || 'Home',
      href: '/',
      icon: routeIcons[''],
    });

    // Build breadcrumbs from path segments
    let currentPath = '';
    pathSegments.forEach((segment, _index) => {
      currentPath += `/${segment}`;
      
      // Decode URL-encoded segments
      const decodedSegment = decodeURIComponent(segment);
      
      // Get label from routeLabels or format the segment
      const label = routeLabels[decodedSegment] || 
                   decodedSegment.charAt(0).toUpperCase() + decodedSegment.slice(1).replace(/-/g, ' ');
      
      breadcrumbs.push({
        label,
        href: currentPath,
        icon: routeIcons[decodedSegment],
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  if (breadcrumbs.length <= 1) {
    return null; // Don't show breadcrumbs on home page
  }

  return (
    <nav className="flex items-center space-x-1 text-sm text-secondary-600 mb-6" aria-label="Breadcrumb">
      <ol className="flex items-center space-x-1">
        {breadcrumbs.map((item, index) => {
          const isLast = index === breadcrumbs.length - 1;
          
          return (
            <li key={item.href} className="flex items-center">
              {index > 0 && (
                <svg
                  className={cn(
                    'h-4 w-4 mx-2 text-secondary-400',
                    isRTL && 'rotate-180'
                  )}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              )}
              
              {isLast ? (
                <span className="flex items-center space-x-1 text-secondary-900 font-medium">
                  {item.icon && <span>{item.icon}</span>}
                  <span>{item.label}</span>
                </span>
              ) : (
                <Link
                  href={item.href as any}
                  className="flex items-center space-x-1 hover:text-secondary-900 transition-colors"
                >
                  {item.icon && <span>{item.icon}</span>}
                  <span>{item.label}</span>
                </Link>
              )}
            </li>
          );
        })}
      </ol>
    </nav>
  );
}
