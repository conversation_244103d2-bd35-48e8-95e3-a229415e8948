'use client';

import React from 'react';

import { Button } from '@/components/ui';
import { useTheme } from '@/contexts/ThemeContext';

const themeIcons = {
  light: '☀️',
  dark: '🌙',
  system: '💻',
};

const themeLabels = {
  light: 'Light',
  dark: 'Dark',
  system: 'System',
};

export function ThemeToggle() {
  const { theme, toggleTheme } = useTheme();
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleTheme}
      className="gap-2"
      title={`Current theme: ${themeLabels[theme]}`}
    >
      <span>{themeIcons[theme]}</span>
      <span className="hidden sm:inline">{themeLabels[theme]}</span>
    </Button>
  );
}
