'use client';

import React, { useState } from 'react';

import { useLanguage } from '@/contexts/LanguageContext';
import { cn } from '@/utils';

import { Breadcrumbs } from './Breadcrumbs';
import { Header } from './Header';
import { SidebarNavigation } from './SidebarNavigation';

interface PortalLayoutContentProps {
  children: React.ReactNode;
}

export function PortalLayoutContent({ children }: PortalLayoutContentProps) {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const { isRTL } = useLanguage();

  return (
    <div className={cn('min-h-screen bg-gray-50', isRTL && 'rtl')}>
      {/* Header */}
      <Header />
      
      <div className="flex">
        {/* Sidebar */}
        <aside className="hidden lg:block">
          <SidebarNavigation
            isCollapsed={isSidebarCollapsed}
            onToggleCollapse={() => setIsSidebarCollapsed(!isSidebarCollapsed)}
          />
        </aside>

        {/* Main Content */}
        <main className="flex-1 min-h-screen">
          <div className="p-6">
            {/* Breadcrumbs */}
            <Breadcrumbs />
            
            {/* Page Content */}
            <div className="max-w-full">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-secondary-200 mt-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-sm font-semibold text-secondary-900 mb-4">Company</h3>
              <ul className="space-y-2 text-sm text-secondary-600">
                <li><a href="#" className="hover:text-secondary-900">About Us</a></li>
                <li><a href="#" className="hover:text-secondary-900">Careers</a></li>
                <li><a href="#" className="hover:text-secondary-900">Press</a></li>
                <li><a href="#" className="hover:text-secondary-900">Blog</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-secondary-900 mb-4">Support</h3>
              <ul className="space-y-2 text-sm text-secondary-600">
                <li><a href="#" className="hover:text-secondary-900">Help Center</a></li>
                <li><a href="#" className="hover:text-secondary-900">Contact Us</a></li>
                <li><a href="#" className="hover:text-secondary-900">Returns</a></li>
                <li><a href="#" className="hover:text-secondary-900">Shipping Info</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-secondary-900 mb-4">Legal</h3>
              <ul className="space-y-2 text-sm text-secondary-600">
                <li><a href="#" className="hover:text-secondary-900">Privacy Policy</a></li>
                <li><a href="#" className="hover:text-secondary-900">Terms of Service</a></li>
                <li><a href="#" className="hover:text-secondary-900">Cookie Policy</a></li>
                <li><a href="#" className="hover:text-secondary-900">GDPR</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-sm font-semibold text-secondary-900 mb-4">Connect</h3>
              <ul className="space-y-2 text-sm text-secondary-600">
                <li><a href="#" className="hover:text-secondary-900">Twitter</a></li>
                <li><a href="#" className="hover:text-secondary-900">LinkedIn</a></li>
                <li><a href="#" className="hover:text-secondary-900">Facebook</a></li>
                <li><a href="#" className="hover:text-secondary-900">Instagram</a></li>
              </ul>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-secondary-200">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-primary-600 rounded flex items-center justify-center">
                  <span className="text-white font-bold text-sm">E</span>
                </div>
                <span className="text-sm text-secondary-600">
                  © 2024 ElectroShop. All rights reserved.
                </span>
              </div>
              
              <div className="flex items-center space-x-4 mt-4 md:mt-0">
                <span className="text-sm text-secondary-600">Powered by Next.js</span>
                <div className="flex space-x-2">
                  <span className="text-lg">💳</span>
                  <span className="text-lg">🔒</span>
                  <span className="text-lg">✅</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
