'use client';

import React from 'react';

import { Badge, Button, Dropdown } from '@/components/ui';
import { removeFromCart, updateQuantity } from '@/lib/redux/slices/cartSlice';
import { useAppSelector, useAppDispatch } from '@/lib/redux/store';
import { formatCurrency } from '@/utils';

export function CartNotification() {
  const dispatch = useAppDispatch();
  const { items, totalItems, totalAmount, currency } = useAppSelector(state => state.cart);
  
  const handleRemoveItem = (itemId: string) => {
    dispatch(removeFromCart(itemId));
  };

  const handleUpdateQuantity = (itemId: string, quantity: number) => {
    dispatch(updateQuantity({ itemId, quantity }));
  };

  const cartItems = items.map(item => ({
    key: item.id,
    label: (
      <div className="w-80 p-2">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-secondary-100 rounded flex items-center justify-center">
            📱
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-sm font-medium text-secondary-900 truncate">
              {item.product.name}
            </div>
            <div className="text-xs text-secondary-500">
              {formatCurrency(item.product.price, currency)}
            </div>
            <div className="flex items-center space-x-2 mt-1">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleUpdateQuantity(item.id, Math.max(1, item.quantity - 1));
                }}
                className="w-6 h-6 rounded border border-secondary-300 flex items-center justify-center text-xs hover:bg-secondary-50"
              >
                -
              </button>
              <span className="text-xs font-medium w-8 text-center">{item.quantity}</span>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleUpdateQuantity(item.id, item.quantity + 1);
                }}
                className="w-6 h-6 rounded border border-secondary-300 flex items-center justify-center text-xs hover:bg-secondary-50"
              >
                +
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleRemoveItem(item.id);
                }}
                className="text-error-600 hover:text-error-700 text-xs ml-2"
              >
                Remove
              </button>
            </div>
          </div>
        </div>
      </div>
    ),
    onClick: () => {},
  }));

  const dropdownItems = [
    ...cartItems,
    ...(items.length > 0 ? [
      {
        key: 'divider',
        label: <div className="border-t border-secondary-200 my-2" />,
        onClick: () => {},
      },
      {
        key: 'total',
        label: (
          <div className="p-2">
            <div className="flex justify-between items-center font-medium">
              <span>Total:</span>
              <span>{formatCurrency(totalAmount, currency)}</span>
            </div>
          </div>
        ),
        onClick: () => {},
      },
      {
        key: 'checkout',
        label: (
          <div className="p-2">
            <Button fullWidth size="sm">
              Checkout
            </Button>
          </div>
        ),
        onClick: () => {
          // Navigate to checkout
          window.location.href = '/checkout';
        },
      },
    ] : [
      {
        key: 'empty',
        label: (
          <div className="p-4 text-center text-secondary-500">
            Your cart is empty
          </div>
        ),
        onClick: () => {},
      },
    ]),
  ];

  return (
    <Dropdown
      trigger={
        <Button variant="ghost" size="sm" className="relative">
          <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4" />
          </svg>
          {totalItems > 0 && (
            <Badge 
              variant="error" 
              size="sm" 
              className="absolute -top-1 -right-1 min-w-5 h-5 flex items-center justify-center text-xs"
            >
              {totalItems}
            </Badge>
          )}
        </Button>
      }
      items={dropdownItems}
      align="right"
      menuClassName="max-h-96 overflow-y-auto"
    />
  );
}
