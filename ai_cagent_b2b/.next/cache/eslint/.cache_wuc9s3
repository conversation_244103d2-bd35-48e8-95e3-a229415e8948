[{"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx": "1", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/contracts/page.tsx": "2", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/dashboard/page.tsx": "3", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx": "4", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/orders/page.tsx": "5", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/page.tsx": "6", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/products/page.tsx": "7", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/profile/page.tsx": "8", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx": "9", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/LanguageContext.tsx": "10", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/ThemeContext.tsx": "11", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/config.ts": "12", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/request.ts": "13", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/cartSlice.ts": "14", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/ordersSlice.ts": "15", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/productsSlice.ts": "16", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/userSlice.ts": "17", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/store.ts": "18", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/types/index.ts": "19", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/index.ts": "20", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Alert/index.tsx": "21", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Badge/index.tsx": "22", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Button/index.tsx": "23", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Card/index.tsx": "24", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx": "25", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Modal/index.tsx": "26", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/index.ts": "27"}, {"size": 1553, "mtime": 1751532921026, "results": "28", "hashOfConfig": "29"}, {"size": 432, "mtime": 1751531918534, "results": "30", "hashOfConfig": "29"}, {"size": 427, "mtime": 1751531901888, "results": "31", "hashOfConfig": "29"}, {"size": 841, "mtime": 1751532885791, "results": "32", "hashOfConfig": "29"}, {"size": 423, "mtime": 1751531894462, "results": "33", "hashOfConfig": "29"}, {"size": 7422, "mtime": 1751533096412, "results": "34", "hashOfConfig": "29"}, {"size": 5473, "mtime": 1751533473474, "results": "35", "hashOfConfig": "29"}, {"size": 421, "mtime": 1751531924763, "results": "36", "hashOfConfig": "29"}, {"size": 575, "mtime": 1751532866467, "results": "37", "hashOfConfig": "29"}, {"size": 1972, "mtime": 1751532859482, "results": "38", "hashOfConfig": "29"}, {"size": 2549, "mtime": 1751532847440, "results": "39", "hashOfConfig": "29"}, {"size": 1169, "mtime": 1751531790943, "results": "40", "hashOfConfig": "29"}, {"size": 418, "mtime": 1751532970299, "results": "41", "hashOfConfig": "29"}, {"size": 4240, "mtime": 1751532939511, "results": "42", "hashOfConfig": "29"}, {"size": 6834, "mtime": 1751532984769, "results": "43", "hashOfConfig": "29"}, {"size": 7101, "mtime": 1751532758917, "results": "44", "hashOfConfig": "29"}, {"size": 5725, "mtime": 1751532831226, "results": "45", "hashOfConfig": "29"}, {"size": 998, "mtime": 1751532730211, "results": "46", "hashOfConfig": "29"}, {"size": 3433, "mtime": 1751532899209, "results": "47", "hashOfConfig": "29"}, {"size": 3569, "mtime": 1751531732960, "results": "48", "hashOfConfig": "29"}, {"size": 4485, "mtime": 1751533255772, "results": "49", "hashOfConfig": "29"}, {"size": 1924, "mtime": 1751533229887, "results": "50", "hashOfConfig": "29"}, {"size": 2918, "mtime": 1751533166060, "results": "51", "hashOfConfig": "29"}, {"size": 2854, "mtime": 1751533216433, "results": "52", "hashOfConfig": "29"}, {"size": 3087, "mtime": 1751533502268, "results": "53", "hashOfConfig": "29"}, {"size": 4939, "mtime": 1751533280794, "results": "54", "hashOfConfig": "29"}, {"size": 178, "mtime": 1751533428005, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l073p1", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/contracts/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/dashboard/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/orders/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/page.tsx", ["137"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/products/page.tsx", ["138", "139", "140"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/profile/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/LanguageContext.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/ThemeContext.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/config.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/request.ts", ["141"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/cartSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/ordersSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/productsSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/userSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/store.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/types/index.ts", ["142"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/index.ts", ["143", "144", "145"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Alert/index.tsx", ["146"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Badge/index.tsx", ["147"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Button/index.tsx", ["148"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Card/index.tsx", ["149"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx", ["150"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Modal/index.tsx", ["151"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/index.ts", [], [], {"ruleId": "152", "severity": 2, "message": "153", "line": 7, "column": 1, "nodeType": "154", "endLine": 7, "endColumn": 66, "fix": "155"}, {"ruleId": "152", "severity": 2, "message": "153", "line": 7, "column": 1, "nodeType": "154", "endLine": 7, "endColumn": 82, "fix": "156"}, {"ruleId": "152", "severity": 2, "message": "157", "line": 8, "column": 1, "nodeType": "154", "endLine": 8, "endColumn": 58, "fix": "158"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 19, "column": 37, "nodeType": "161", "messageId": "162", "endLine": 19, "endColumn": 40, "suggestions": "163"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 7, "column": 43, "nodeType": "161", "messageId": "162", "endLine": 7, "endColumn": 46, "suggestions": "164"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 68, "column": 34, "nodeType": "161", "messageId": "162", "endLine": 68, "endColumn": 37, "suggestions": "165"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 47, "column": 46, "nodeType": "161", "messageId": "162", "endLine": 47, "endColumn": 49, "suggestions": "166"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 47, "column": 56, "nodeType": "161", "messageId": "162", "endLine": 47, "endColumn": 59, "suggestions": "167"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 115, "column": 30, "nodeType": "161", "messageId": "162", "endLine": 115, "endColumn": 33, "suggestions": "168"}, {"ruleId": "152", "severity": 2, "message": "169", "line": 2, "column": 1, "nodeType": "154", "endLine": 2, "endColumn": 67, "fix": "170"}, {"ruleId": "152", "severity": 2, "message": "169", "line": 2, "column": 1, "nodeType": "154", "endLine": 2, "endColumn": 67, "fix": "171"}, {"ruleId": "152", "severity": 2, "message": "169", "line": 2, "column": 1, "nodeType": "154", "endLine": 2, "endColumn": 67, "fix": "172"}, {"ruleId": "152", "severity": 2, "message": "169", "line": 2, "column": 1, "nodeType": "154", "endLine": 2, "endColumn": 67, "fix": "173"}, {"ruleId": "152", "severity": 2, "message": "169", "line": 2, "column": 1, "nodeType": "154", "endLine": 2, "endColumn": 67, "fix": "174"}, {"ruleId": "152", "severity": 2, "message": "169", "line": 4, "column": 1, "nodeType": "154", "endLine": 4, "endColumn": 67, "fix": "175"}, "import/order", "`@/lib/redux/slices/productsSlice` import should occur before import of `@/lib/redux/store`", "ImportDeclaration", {"range": "176", "text": "177"}, {"range": "178", "text": "179"}, "`@/lib/redux/slices/cartSlice` import should occur before import of `@/lib/redux/store`", {"range": "180", "text": "181"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["182", "183"], ["184", "185"], ["186", "187"], ["188", "189"], ["190", "191"], ["192", "193"], "`class-variance-authority` import should occur before import of `react`", {"range": "194", "text": "195"}, {"range": "196", "text": "195"}, {"range": "197", "text": "195"}, {"range": "198", "text": "195"}, {"range": "199", "text": "195"}, {"range": "200", "text": "195"}, [81, 215], "import { fetchProducts } from '@/lib/redux/slices/productsSlice';\nimport { useAppDispatch, useAppSelector } from '@/lib/redux/store';\n", [244, 394], "import { fetchProducts, setSearchQuery } from '@/lib/redux/slices/productsSlice';\nimport { useAppDispatch, useAppSelector } from '@/lib/redux/store';\n", [244, 452], "import { addToCart } from '@/lib/redux/slices/cartSlice';\nimport { useAppDispatch, useAppSelector } from '@/lib/redux/store';\nimport { fetchProducts, setSearchQuery } from '@/lib/redux/slices/productsSlice';\n", {"messageId": "201", "fix": "202", "desc": "203"}, {"messageId": "204", "fix": "205", "desc": "206"}, {"messageId": "201", "fix": "207", "desc": "203"}, {"messageId": "204", "fix": "208", "desc": "206"}, {"messageId": "201", "fix": "209", "desc": "203"}, {"messageId": "204", "fix": "210", "desc": "206"}, {"messageId": "201", "fix": "211", "desc": "203"}, {"messageId": "204", "fix": "212", "desc": "206"}, {"messageId": "201", "fix": "213", "desc": "203"}, {"messageId": "204", "fix": "214", "desc": "206"}, {"messageId": "201", "fix": "215", "desc": "203"}, {"messageId": "204", "fix": "216", "desc": "206"}, [0, 94], "import { cva, type VariantProps } from 'class-variance-authority';\nimport React from 'react';\n", [0, 94], [0, 94], [0, 94], [0, 94], [15, 109], "suggestUnknown", {"range": "217", "text": "218"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "219", "text": "220"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "221", "text": "218"}, {"range": "222", "text": "220"}, {"range": "223", "text": "218"}, {"range": "224", "text": "220"}, {"range": "225", "text": "218"}, {"range": "226", "text": "220"}, {"range": "227", "text": "218"}, {"range": "228", "text": "220"}, {"range": "229", "text": "218"}, {"range": "230", "text": "220"}, [792, 795], "unknown", [792, 795], "never", [249, 252], [249, 252], [1182, 1185], [1182, 1185], [992, 995], [992, 995], [1002, 1005], [1002, 1005], [2507, 2510], [2507, 2510]]