[{"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx": "1", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx": "2", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/page.tsx": "3", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx": "4", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/LanguageContext.tsx": "5", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/ThemeContext.tsx": "6", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/config.ts": "7", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/request.ts": "8", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/cartSlice.ts": "9", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/ordersSlice.ts": "10", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/productsSlice.ts": "11", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/userSlice.ts": "12", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/store.ts": "13", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/types/index.ts": "14", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/index.ts": "15", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Alert/index.tsx": "16", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Badge/index.tsx": "17", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Button/index.tsx": "18", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Card/index.tsx": "19", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx": "20", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Modal/index.tsx": "21", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/index.ts": "22", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Dropdown/index.tsx": "23", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Select/index.tsx": "24", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Toggle/index.tsx": "25", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/rtl.ts": "26", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/theme.ts": "27", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/contracts/page.tsx": "28", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx": "29", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/orders/page.tsx": "30", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx": "31", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/profile/page.tsx": "32", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Breadcrumbs.tsx": "33", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/CartNotification.tsx": "34", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Header.tsx": "35", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/LanguageToggle.tsx": "36", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx": "37", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SearchAutocomplete.tsx": "38", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SidebarNavigation.tsx": "39", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ThemeToggle.tsx": "40"}, {"size": 323, "mtime": 1751535074484, "results": "41", "hashOfConfig": "42"}, {"size": 841, "mtime": 1751532885791, "results": "43", "hashOfConfig": "42"}, {"size": 7422, "mtime": 1751533527186, "results": "44", "hashOfConfig": "42"}, {"size": 575, "mtime": 1751532866467, "results": "45", "hashOfConfig": "42"}, {"size": 1972, "mtime": 1751532859482, "results": "46", "hashOfConfig": "42"}, {"size": 2549, "mtime": 1751532847440, "results": "47", "hashOfConfig": "42"}, {"size": 1169, "mtime": 1751531790943, "results": "48", "hashOfConfig": "42"}, {"size": 418, "mtime": 1751532970299, "results": "49", "hashOfConfig": "42"}, {"size": 4240, "mtime": 1751532939511, "results": "50", "hashOfConfig": "42"}, {"size": 6834, "mtime": 1751532984769, "results": "51", "hashOfConfig": "42"}, {"size": 7101, "mtime": 1751532758917, "results": "52", "hashOfConfig": "42"}, {"size": 5725, "mtime": 1751532831226, "results": "53", "hashOfConfig": "42"}, {"size": 998, "mtime": 1751532730211, "results": "54", "hashOfConfig": "42"}, {"size": 3433, "mtime": 1751532899209, "results": "55", "hashOfConfig": "42"}, {"size": 3569, "mtime": 1751531732960, "results": "56", "hashOfConfig": "42"}, {"size": 4485, "mtime": 1751534680274, "results": "57", "hashOfConfig": "42"}, {"size": 1924, "mtime": 1751534668782, "results": "58", "hashOfConfig": "42"}, {"size": 2918, "mtime": 1751534635349, "results": "59", "hashOfConfig": "42"}, {"size": 2854, "mtime": 1751534657784, "results": "60", "hashOfConfig": "42"}, {"size": 3087, "mtime": 1751534645936, "results": "61", "hashOfConfig": "42"}, {"size": 4939, "mtime": 1751534691499, "results": "62", "hashOfConfig": "42"}, {"size": 258, "mtime": 1751534761336, "results": "63", "hashOfConfig": "42"}, {"size": 2619, "mtime": 1751534821570, "results": "64", "hashOfConfig": "42"}, {"size": 5918, "mtime": 1751534737875, "results": "65", "hashOfConfig": "42"}, {"size": 3200, "mtime": 1751534713054, "results": "66", "hashOfConfig": "42"}, {"size": 3107, "mtime": 1751534801825, "results": "67", "hashOfConfig": "42"}, {"size": 2608, "mtime": 1751534782597, "results": "68", "hashOfConfig": "42"}, {"size": 432, "mtime": 1751531918534, "results": "69", "hashOfConfig": "42"}, {"size": 9125, "mtime": 1751534874541, "results": "70", "hashOfConfig": "42"}, {"size": 423, "mtime": 1751531894462, "results": "71", "hashOfConfig": "42"}, {"size": 5473, "mtime": 1751533540261, "results": "72", "hashOfConfig": "42"}, {"size": 421, "mtime": 1751531924763, "results": "73", "hashOfConfig": "42"}, {"size": 3448, "mtime": 1751535162973, "results": "74", "hashOfConfig": "42"}, {"size": 4557, "mtime": 1751535187517, "results": "75", "hashOfConfig": "42"}, {"size": 5217, "mtime": 1751535281957, "results": "76", "hashOfConfig": "42"}, {"size": 1198, "mtime": 1751534904762, "results": "77", "hashOfConfig": "42"}, {"size": 4945, "mtime": 1751535294686, "results": "78", "hashOfConfig": "42"}, {"size": 5065, "mtime": 1751535318666, "results": "79", "hashOfConfig": "42"}, {"size": 6924, "mtime": 1751535331073, "results": "80", "hashOfConfig": "42"}, {"size": 677, "mtime": 1751534913680, "results": "81", "hashOfConfig": "42"}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l073p1", {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/LanguageContext.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/ThemeContext.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/config.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/request.ts", ["202"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/cartSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/ordersSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/productsSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/userSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/store.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/types/index.ts", ["203"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/index.ts", ["204", "205", "206"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Alert/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Badge/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Button/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Card/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Modal/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/index.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Dropdown/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Select/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Toggle/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/rtl.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/theme.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/contracts/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/orders/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx", ["207"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/profile/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Breadcrumbs.tsx", ["208"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/CartNotification.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Header.tsx", ["209"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/LanguageToggle.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SearchAutocomplete.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SidebarNavigation.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ThemeToggle.tsx", [], [], {"ruleId": "210", "severity": 1, "message": "211", "line": 7, "column": 43, "nodeType": "212", "messageId": "213", "endLine": 7, "endColumn": 46, "suggestions": "214"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 68, "column": 34, "nodeType": "212", "messageId": "213", "endLine": 68, "endColumn": 37, "suggestions": "215"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 47, "column": 46, "nodeType": "212", "messageId": "213", "endLine": 47, "endColumn": 49, "suggestions": "216"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 47, "column": 56, "nodeType": "212", "messageId": "213", "endLine": 47, "endColumn": 59, "suggestions": "217"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 115, "column": 30, "nodeType": "212", "messageId": "213", "endLine": 115, "endColumn": 33, "suggestions": "218"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 19, "column": 37, "nodeType": "212", "messageId": "213", "endLine": 19, "endColumn": 40, "suggestions": "219"}, {"ruleId": "210", "severity": 1, "message": "211", "line": 110, "column": 38, "nodeType": "212", "messageId": "213", "endLine": 110, "endColumn": 41, "suggestions": "220"}, {"ruleId": "221", "severity": 2, "message": "222", "line": 30, "column": 18, "nodeType": "223", "messageId": "224", "endLine": 30, "endColumn": 24}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["225", "226"], ["227", "228"], ["229", "230"], ["231", "232"], ["233", "234"], ["235", "236"], ["237", "238"], "@typescript-eslint/no-unused-vars", "'_isRTL' is assigned a value but never used.", "Identifier", "unusedVar", {"messageId": "239", "fix": "240", "desc": "241"}, {"messageId": "242", "fix": "243", "desc": "244"}, {"messageId": "239", "fix": "245", "desc": "241"}, {"messageId": "242", "fix": "246", "desc": "244"}, {"messageId": "239", "fix": "247", "desc": "241"}, {"messageId": "242", "fix": "248", "desc": "244"}, {"messageId": "239", "fix": "249", "desc": "241"}, {"messageId": "242", "fix": "250", "desc": "244"}, {"messageId": "239", "fix": "251", "desc": "241"}, {"messageId": "242", "fix": "252", "desc": "244"}, {"messageId": "239", "fix": "253", "desc": "241"}, {"messageId": "242", "fix": "254", "desc": "244"}, {"messageId": "239", "fix": "255", "desc": "241"}, {"messageId": "242", "fix": "256", "desc": "244"}, "suggestUnknown", {"range": "257", "text": "258"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "259", "text": "260"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "261", "text": "258"}, {"range": "262", "text": "260"}, {"range": "263", "text": "258"}, {"range": "264", "text": "260"}, {"range": "265", "text": "258"}, {"range": "266", "text": "260"}, {"range": "267", "text": "258"}, {"range": "268", "text": "260"}, {"range": "269", "text": "258"}, {"range": "270", "text": "260"}, {"range": "271", "text": "258"}, {"range": "272", "text": "260"}, [249, 252], "unknown", [249, 252], "never", [1182, 1185], [1182, 1185], [992, 995], [992, 995], [1002, 1005], [1002, 1005], [2507, 2510], [2507, 2510], [792, 795], [792, 795], [3090, 3093], [3090, 3093]]