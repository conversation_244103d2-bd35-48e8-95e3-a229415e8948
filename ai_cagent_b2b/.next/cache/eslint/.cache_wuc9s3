[{"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx": "1", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx": "2", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/page.tsx": "3", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx": "4", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/LanguageContext.tsx": "5", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/ThemeContext.tsx": "6", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/config.ts": "7", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/request.ts": "8", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/cartSlice.ts": "9", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/ordersSlice.ts": "10", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/productsSlice.ts": "11", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/userSlice.ts": "12", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/store.ts": "13", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/types/index.ts": "14", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/index.ts": "15", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Alert/index.tsx": "16", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Badge/index.tsx": "17", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Button/index.tsx": "18", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Card/index.tsx": "19", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx": "20", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Modal/index.tsx": "21", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/index.ts": "22", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Dropdown/index.tsx": "23", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Select/index.tsx": "24", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Toggle/index.tsx": "25", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/rtl.ts": "26", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/theme.ts": "27", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/contracts/page.tsx": "28", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx": "29", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/orders/page.tsx": "30", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx": "31", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/profile/page.tsx": "32", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Breadcrumbs.tsx": "33", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/CartNotification.tsx": "34", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Header.tsx": "35", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/LanguageToggle.tsx": "36", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx": "37", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SearchAutocomplete.tsx": "38", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SidebarNavigation.tsx": "39", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ThemeToggle.tsx": "40", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/translate.ts": "41", "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/useTranslation.ts": "42"}, {"size": 323, "mtime": 1751535074484, "results": "43", "hashOfConfig": "44"}, {"size": 841, "mtime": 1751532885791, "results": "45", "hashOfConfig": "44"}, {"size": 7422, "mtime": 1751533527186, "results": "46", "hashOfConfig": "44"}, {"size": 575, "mtime": 1751532866467, "results": "47", "hashOfConfig": "44"}, {"size": 1972, "mtime": 1751532859482, "results": "48", "hashOfConfig": "44"}, {"size": 2549, "mtime": 1751532847440, "results": "49", "hashOfConfig": "44"}, {"size": 1169, "mtime": 1751531790943, "results": "50", "hashOfConfig": "44"}, {"size": 418, "mtime": 1751532970299, "results": "51", "hashOfConfig": "44"}, {"size": 4240, "mtime": 1751532939511, "results": "52", "hashOfConfig": "44"}, {"size": 6834, "mtime": 1751532984769, "results": "53", "hashOfConfig": "44"}, {"size": 7101, "mtime": 1751532758917, "results": "54", "hashOfConfig": "44"}, {"size": 5725, "mtime": 1751532831226, "results": "55", "hashOfConfig": "44"}, {"size": 998, "mtime": 1751532730211, "results": "56", "hashOfConfig": "44"}, {"size": 3433, "mtime": 1751532899209, "results": "57", "hashOfConfig": "44"}, {"size": 3569, "mtime": 1751531732960, "results": "58", "hashOfConfig": "44"}, {"size": 4485, "mtime": 1751534680274, "results": "59", "hashOfConfig": "44"}, {"size": 1924, "mtime": 1751534668782, "results": "60", "hashOfConfig": "44"}, {"size": 2918, "mtime": 1751534635349, "results": "61", "hashOfConfig": "44"}, {"size": 2854, "mtime": 1751534657784, "results": "62", "hashOfConfig": "44"}, {"size": 3087, "mtime": 1751534645936, "results": "63", "hashOfConfig": "44"}, {"size": 4939, "mtime": 1751534691499, "results": "64", "hashOfConfig": "44"}, {"size": 258, "mtime": 1751534761336, "results": "65", "hashOfConfig": "44"}, {"size": 2685, "mtime": 1751535621858, "results": "66", "hashOfConfig": "44"}, {"size": 5918, "mtime": 1751534737875, "results": "67", "hashOfConfig": "44"}, {"size": 3200, "mtime": 1751534713054, "results": "68", "hashOfConfig": "44"}, {"size": 3107, "mtime": 1751534801825, "results": "69", "hashOfConfig": "44"}, {"size": 2608, "mtime": 1751534782597, "results": "70", "hashOfConfig": "44"}, {"size": 432, "mtime": 1751531918534, "results": "71", "hashOfConfig": "44"}, {"size": 9125, "mtime": 1751534874541, "results": "72", "hashOfConfig": "44"}, {"size": 423, "mtime": 1751531894462, "results": "73", "hashOfConfig": "44"}, {"size": 5668, "mtime": 1751536067582, "results": "74", "hashOfConfig": "44"}, {"size": 421, "mtime": 1751531924763, "results": "75", "hashOfConfig": "44"}, {"size": 3448, "mtime": 1751535162973, "results": "76", "hashOfConfig": "44"}, {"size": 4557, "mtime": 1751535187517, "results": "77", "hashOfConfig": "44"}, {"size": 5130, "mtime": 1751535659884, "results": "78", "hashOfConfig": "44"}, {"size": 1244, "mtime": 1751536222038, "results": "79", "hashOfConfig": "44"}, {"size": 4945, "mtime": 1751535294686, "results": "80", "hashOfConfig": "44"}, {"size": 5065, "mtime": 1751535318666, "results": "81", "hashOfConfig": "44"}, {"size": 6931, "mtime": 1751535673515, "results": "82", "hashOfConfig": "44"}, {"size": 677, "mtime": 1751534913680, "results": "83", "hashOfConfig": "44"}, {"size": 5285, "mtime": 1751536248777, "results": "84", "hashOfConfig": "44"}, {"size": 3283, "mtime": 1751535909506, "results": "85", "hashOfConfig": "44"}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "l073p1", {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/LanguageContext.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/contexts/ThemeContext.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/config.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/request.ts", ["212"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/cartSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/ordersSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/productsSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/slices/userSlice.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/redux/store.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/types/index.ts", ["213"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/index.ts", ["214", "215", "216"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Alert/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Badge/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Button/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Card/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Modal/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/index.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Dropdown/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Select/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Toggle/index.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/rtl.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/utils/theme.ts", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/contracts/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/orders/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx", ["217"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/profile/page.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Breadcrumbs.tsx", ["218"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/CartNotification.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Header.tsx", ["219", "220"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/LanguageToggle.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SearchAutocomplete.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SidebarNavigation.tsx", ["221"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ThemeToggle.tsx", [], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/translate.ts", ["222", "223", "224", "225", "226", "227"], [], "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/lib/i18n/useTranslation.ts", ["228", "229", "230"], ["231"], {"ruleId": "232", "severity": 1, "message": "233", "line": 7, "column": 43, "nodeType": "234", "messageId": "235", "endLine": 7, "endColumn": 46, "suggestions": "236"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 68, "column": 34, "nodeType": "234", "messageId": "235", "endLine": 68, "endColumn": 37, "suggestions": "237"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 47, "column": 46, "nodeType": "234", "messageId": "235", "endLine": 47, "endColumn": 49, "suggestions": "238"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 47, "column": 56, "nodeType": "234", "messageId": "235", "endLine": 47, "endColumn": 59, "suggestions": "239"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 115, "column": 30, "nodeType": "234", "messageId": "235", "endLine": 115, "endColumn": 33, "suggestions": "240"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 22, "column": 37, "nodeType": "234", "messageId": "235", "endLine": 22, "endColumn": 40, "suggestions": "241"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 110, "column": 38, "nodeType": "234", "messageId": "235", "endLine": 110, "endColumn": 41, "suggestions": "242"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 51, "column": 36, "nodeType": "234", "messageId": "235", "endLine": 51, "endColumn": 39, "suggestions": "243"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 131, "column": 36, "nodeType": "234", "messageId": "235", "endLine": 131, "endColumn": 39, "suggestions": "244"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 106, "column": 32, "nodeType": "234", "messageId": "235", "endLine": 106, "endColumn": 35, "suggestions": "245"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 4, "column": 55, "nodeType": "234", "messageId": "235", "endLine": 4, "endColumn": 58, "suggestions": "246"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 9, "column": 103, "nodeType": "234", "messageId": "235", "endLine": 9, "endColumn": 106, "suggestions": "247"}, {"ruleId": "248", "severity": 1, "message": "249", "line": 22, "column": 5, "nodeType": "250", "messageId": "251", "endLine": 22, "endColumn": 17, "suggestions": "252"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 34, "column": 45, "nodeType": "234", "messageId": "235", "endLine": 34, "endColumn": 48, "suggestions": "253"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 35, "column": 51, "nodeType": "234", "messageId": "235", "endLine": 35, "endColumn": 54, "suggestions": "254"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 76, "column": 32, "nodeType": "234", "messageId": "235", "endLine": 76, "endColumn": 35, "suggestions": "255"}, {"ruleId": "232", "severity": 1, "message": "233", "line": 26, "column": 67, "nodeType": "234", "messageId": "235", "endLine": 26, "endColumn": 70, "suggestions": "256"}, {"ruleId": "248", "severity": 1, "message": "249", "line": 40, "column": 9, "nodeType": "250", "messageId": "251", "endLine": 40, "endColumn": 21, "suggestions": "257"}, {"ruleId": "248", "severity": 1, "message": "249", "line": 49, "column": 13, "nodeType": "250", "messageId": "251", "endLine": 49, "endColumn": 26, "suggestions": "258"}, {"ruleId": "259", "severity": 2, "message": "260", "line": 97, "column": 26, "nodeType": "261", "endLine": 97, "endColumn": 40, "suppressions": "262"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["263", "264"], ["265", "266"], ["267", "268"], ["269", "270"], ["271", "272"], ["273", "274"], ["275", "276"], ["277", "278"], ["279", "280"], ["281", "282"], ["283", "284"], ["285", "286"], "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", ["287"], ["288", "289"], ["290", "291"], ["292", "293"], ["294", "295"], ["296"], ["297"], "react-hooks/rules-of-hooks", "React Hook \"useTranslation\" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.", "Identifier", ["298"], {"messageId": "299", "fix": "300", "desc": "301"}, {"messageId": "302", "fix": "303", "desc": "304"}, {"messageId": "299", "fix": "305", "desc": "301"}, {"messageId": "302", "fix": "306", "desc": "304"}, {"messageId": "299", "fix": "307", "desc": "301"}, {"messageId": "302", "fix": "308", "desc": "304"}, {"messageId": "299", "fix": "309", "desc": "301"}, {"messageId": "302", "fix": "310", "desc": "304"}, {"messageId": "299", "fix": "311", "desc": "301"}, {"messageId": "302", "fix": "312", "desc": "304"}, {"messageId": "299", "fix": "313", "desc": "301"}, {"messageId": "302", "fix": "314", "desc": "304"}, {"messageId": "299", "fix": "315", "desc": "301"}, {"messageId": "302", "fix": "316", "desc": "304"}, {"messageId": "299", "fix": "317", "desc": "301"}, {"messageId": "302", "fix": "318", "desc": "304"}, {"messageId": "299", "fix": "319", "desc": "301"}, {"messageId": "302", "fix": "320", "desc": "304"}, {"messageId": "299", "fix": "321", "desc": "301"}, {"messageId": "302", "fix": "322", "desc": "304"}, {"messageId": "299", "fix": "323", "desc": "301"}, {"messageId": "302", "fix": "324", "desc": "304"}, {"messageId": "299", "fix": "325", "desc": "301"}, {"messageId": "302", "fix": "326", "desc": "304"}, {"messageId": "327", "data": "328", "fix": "329", "desc": "330"}, {"messageId": "299", "fix": "331", "desc": "301"}, {"messageId": "302", "fix": "332", "desc": "304"}, {"messageId": "299", "fix": "333", "desc": "301"}, {"messageId": "302", "fix": "334", "desc": "304"}, {"messageId": "299", "fix": "335", "desc": "301"}, {"messageId": "302", "fix": "336", "desc": "304"}, {"messageId": "299", "fix": "337", "desc": "301"}, {"messageId": "302", "fix": "338", "desc": "304"}, {"messageId": "327", "data": "339", "fix": "340", "desc": "330"}, {"messageId": "327", "data": "341", "fix": "342", "desc": "343"}, {"kind": "344", "justification": "345"}, "suggestUnknown", {"range": "346", "text": "347"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "348", "text": "349"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "350", "text": "347"}, {"range": "351", "text": "349"}, {"range": "352", "text": "347"}, {"range": "353", "text": "349"}, {"range": "354", "text": "347"}, {"range": "355", "text": "349"}, {"range": "356", "text": "347"}, {"range": "357", "text": "349"}, {"range": "358", "text": "347"}, {"range": "359", "text": "349"}, {"range": "360", "text": "347"}, {"range": "361", "text": "349"}, {"range": "362", "text": "347"}, {"range": "363", "text": "349"}, {"range": "364", "text": "347"}, {"range": "365", "text": "349"}, {"range": "366", "text": "347"}, {"range": "367", "text": "349"}, {"range": "368", "text": "347"}, {"range": "369", "text": "349"}, {"range": "370", "text": "347"}, {"range": "371", "text": "349"}, "removeConsole", {"propertyName": "372"}, {"range": "373", "text": "345"}, "Remove the console.warn().", {"range": "374", "text": "347"}, {"range": "375", "text": "349"}, {"range": "376", "text": "347"}, {"range": "377", "text": "349"}, {"range": "378", "text": "347"}, {"range": "379", "text": "349"}, {"range": "380", "text": "347"}, {"range": "381", "text": "349"}, {"propertyName": "372"}, {"range": "382", "text": "345"}, {"propertyName": "383"}, {"range": "384", "text": "345"}, "Remove the console.error().", "directive", "", [249, 252], "unknown", [249, 252], "never", [1182, 1185], [1182, 1185], [992, 995], [992, 995], [1002, 1005], [1002, 1005], [2507, 2510], [2507, 2510], [947, 950], [947, 950], [3090, 3093], [3090, 3093], [1937, 1940], [1937, 1940], [4688, 4691], [4688, 4691], [3301, 3304], [3301, 3304], [139, 142], [139, 142], [323, 326], [323, 326], "warn", [729, 808], [1060, 1063], [1060, 1063], [1152, 1155], [1152, 1155], [2219, 2222], [2219, 2222], [958, 961], [958, 961], [1389, 1468], "error", [1808, 1878]]