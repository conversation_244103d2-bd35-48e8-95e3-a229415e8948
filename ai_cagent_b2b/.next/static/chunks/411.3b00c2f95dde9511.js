"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[411],{792:e=>{e.exports=JSON.parse('{"title":"Orders","subtitle":"Track and manage your orders","status":{"all":"All Orders","pending":"Pending","confirmed":"Confirmed","processing":"Processing","shipped":"Shipped","delivered":"Delivered","cancelled":"Cancelled","refunded":"Refunded","returned":"Returned"},"actions":{"viewDetails":"View Details","trackOrder":"Track Order","cancelOrder":"Cancel Order","returnOrder":"Return Order","reorder":"Reorder","downloadInvoice":"Download Invoice","contactSupport":"Contact Support"},"details":{"orderNumber":"Order Number","orderDate":"Order Date","estimatedDelivery":"Estimated Delivery","actualDelivery":"Actual Delivery","customer":"Customer","items":"Items","quantity":"Quantity","unitPrice":"Unit Price","totalPrice":"Total Price","subtotal":"Subtotal","shipping":"Shipping","tax":"Tax","discount":"Discount","total":"Total","paymentMethod":"Payment Method","shippingAddress":"Shipping Address","billingAddress":"Billing Address"},"tracking":{"title":"Order Tracking","orderPlaced":"Order Placed","orderConfirmed":"Order Confirmed","inProduction":"In Production","shipped":"Shipped","outForDelivery":"Out for Delivery","delivered":"Delivered","trackingNumber":"Tracking Number","carrier":"Carrier","estimatedDelivery":"Estimated Delivery"},"filters":{"dateRange":"Date Range","status":"Status","customer":"Customer","amount":"Order Amount","apply":"Apply Filters","clear":"Clear Filters"},"search":{"placeholder":"Search orders by number, customer, or product...","noResults":"No orders found"},"pagination":{"showing":"Showing {start} to {end} of {total} orders","previous":"Previous","next":"Next","page":"Page {page}"},"empty":{"title":"No Orders Yet","description":"You haven\'t placed any orders yet. Start shopping to see your orders here.","action":"Start Shopping"},"notifications":{"orderCancelled":"Order has been cancelled successfully","orderReturned":"Return request has been submitted","invoiceDownloaded":"Invoice downloaded successfully"}}')}}]);