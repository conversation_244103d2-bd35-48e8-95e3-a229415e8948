"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[635],{1990:(e,t,r)=>{function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{U1:()=>el,zD:()=>em,Z0:()=>eO});var o,i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),u={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function c(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function l(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function s(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var f=s(),d=Symbol.for("immer-nothing"),p=Symbol.for("immer-draftable"),y=Symbol.for("immer-state");function h(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var b=Object.getPrototypeOf;function _(e){return!!e&&!!e[y]}function w(e){return!!e&&(g(e)||Array.isArray(e)||!!e[p]||!!e.constructor?.[p]||P(e)||j(e))}var m=Object.prototype.constructor.toString();function g(e){if(!e||"object"!=typeof e)return!1;let t=b(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===m}function v(e,t){0===E(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function E(e){let t=e[y];return t?t.type_:Array.isArray(e)?1:P(e)?2:3*!!j(e)}function O(e,t){return 2===E(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function S(e,t,r){let n=E(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function P(e){return e instanceof Map}function j(e){return e instanceof Set}function x(e){return e.copy_||e.base_}function T(e,t){if(P(e))return new Map(e);if(j(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=g(e);if(!0!==t&&("class_only"!==t||r)){let t=b(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[y];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(b(e),t)}}function C(e,t=!1){return A(e)||_(e)||!w(e)||(E(e)>1&&(e.set=e.add=e.clear=e.delete=N),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>C(t,!0))),e}function N(){h(2)}function A(e){return Object.isFrozen(e)}var k={};function D(e){let t=k[e];return t||h(0,e),t}function R(e,t){t&&(D("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function M(e){z(e),e.drafts_.forEach($),e.drafts_=null}function z(e){e===o&&(o=e.parent_)}function F(e){return o={drafts_:[],parent_:o,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function $(e){let t=e[y];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function I(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[y].modified_&&(M(t),h(4)),w(e)&&(e=W(t,e),t.parent_||U(t,e)),t.patches_&&D("Patches").generateReplacementPatches_(r[y].base_,e,t.patches_,t.inversePatches_)):e=W(t,r,[]),M(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==d?e:void 0}function W(e,t,r){if(A(t))return t;let n=t[y];if(!n)return v(t,(o,i)=>L(e,n,t,o,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return U(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),v(o,(o,a)=>L(e,n,t,o,a,r,i)),U(e,t,!1),r&&e.patches_&&D("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function L(e,t,r,n,o,i,a){if(_(o)){let a=W(e,o,i&&t&&3!==t.type_&&!O(t.assigned_,n)?i.concat(n):void 0);if(S(r,n,a),!_(a))return;e.canAutoFreeze_=!1}else a&&r.add(o);if(w(o)&&!A(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;W(e,o),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&U(e,o)}}function U(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&C(t,r)}var V={get(e,t){if(t===y)return e;let r=x(e);if(!O(r,t)){var n=e,o=r,i=t;let a=X(o,i);return a?"value"in a?a.value:a.get?.call(n.draft_):void 0}let a=r[t];return e.finalized_||!w(a)?a:a===q(e.base_,t)?(G(e),e.copy_[t]=Z(a,e)):a},has:(e,t)=>t in x(e),ownKeys:e=>Reflect.ownKeys(x(e)),set(e,t,r){let n=X(x(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=q(x(e),t),o=n?.[y];if(o&&o.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||O(e.base_,t)))return!0;G(e),B(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==q(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,G(e),B(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=x(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){h(11)},getPrototypeOf:e=>b(e.base_),setPrototypeOf(){h(12)}},K={};function q(e,t){let r=e[y];return(r?x(r):e)[t]}function X(e,t){if(!(t in e))return;let r=b(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=b(r)}}function B(e){!e.modified_&&(e.modified_=!0,e.parent_&&B(e.parent_))}function G(e){e.copy_||(e.copy_=T(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function Z(e,t){let r=P(e)?D("MapSet").proxyMap_(e,t):j(e)?D("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),n={type_:+!!r,scope_:t?t.scope_:o,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},i=n,a=V;r&&(i=[n],a=K);let{revoke:u,proxy:c}=Proxy.revocable(i,a);return n.draft_=c,n.revoke_=u,c}(e,t);return(t?t.scope_:o).drafts_.push(r),r}v(V,(e,t)=>{K[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),K.deleteProperty=function(e,t){return K.set.call(this,e,t,void 0)},K.set=function(e,t,r){return V.set.call(this,e[0],t,r,e[0])};var H=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...o){return n.produce(e,e=>t.call(this,e,...o))}}if("function"!=typeof t&&h(6),void 0!==r&&"function"!=typeof r&&h(7),w(e)){let o=F(this),i=Z(e,void 0),a=!0;try{n=t(i),a=!1}finally{a?M(o):z(o)}return R(o,r),I(n,o)}if(e&&"object"==typeof e)h(1,e);else{if(void 0===(n=t(e))&&(n=e),n===d&&(n=void 0),this.autoFreeze_&&C(n,!0),r){let t=[],o=[];D("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){var t;w(e)||h(8),_(e)&&(_(t=e)||h(10,t),e=function e(t){let r;if(!w(t)||A(t))return t;let n=t[y];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=T(t,n.scope_.immer_.useStrictShallowCopy_)}else r=T(t,!0);return v(r,(t,n)=>{S(r,t,e(n))}),n&&(n.finalized_=!1),r}(t));let r=F(this),n=Z(e,void 0);return n[y].isManual_=!0,z(r),n}finishDraft(e,t){let r=e&&e[y];r&&r.isManual_||h(9);let{scope_:n}=r;return R(n,t),I(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=D("Patches").applyPatches_;return _(e)?n(e,t):this.produce(e,e=>n(e,t))}},J=H.produce;H.produceWithPatches.bind(H),H.setAutoFreeze.bind(H),H.setUseStrictShallowCopy.bind(H),H.applyPatches.bind(H),H.createDraft.bind(H),H.finishDraft.bind(H),r(9509);var Q="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?l:l.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var Y=e=>e&&"function"==typeof e.match;function ee(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(eJ(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>c(t)&&"type"in t&&"string"==typeof t.type&&t.type===e,r}function et(e){return["type","payload","error","meta"].indexOf(e)>-1}var er=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function en(e){return w(e)?J(e,()=>{}):e}function eo(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var ei=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},i=new er;return t&&("boolean"==typeof t?i.push(f):i.push(s(t.extraArgument))),i},ea=e=>t=>{setTimeout(t,e)},eu=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),o=!0,i=!1,a=!1,u=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:ea(10):"callback"===e.type?e.queueNotification:ea(e.timeout),l=()=>{a=!1,i&&(i=!1,u.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>o&&e());return u.add(e),()=>{t(),u.delete(e)}},dispatch(e){try{return(i=!(o=!e?.meta?.RTK_autoBatch))&&!a&&(a=!0,c(l)),n.dispatch(e)}finally{o=!0}}})},ec=e=>function(t){let{autoBatch:r=!0}=t??{},n=new er(e);return r&&n.push(eu("object"==typeof r?r:void 0)),n};function el(e){let t,r,o=ei(),{reducer:a,middleware:s,devTools:f=!0,duplicateMiddlewareCheck:d=!0,preloadedState:p,enhancers:y}=e||{};if("function"==typeof a)t=a;else if(c(a))t=function(e){let t,r=Object.keys(e),o={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(o[n]=e[n])}let i=Object.keys(o);try{Object.keys(o).forEach(e=>{let t=o[e];if(void 0===t(void 0,{type:u.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:u.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let a=!1,u={};for(let t=0;t<i.length;t++){let c=i[t],l=o[c],s=e[c],f=l(s,r);if(void 0===f)throw r&&r.type,Error(n(14));u[c]=f,a=a||f!==s}return(a=a||i.length!==Object.keys(e).length)?u:e}}(a);else throw Error(eJ(1));r="function"==typeof s?s(o):o();let h=l;f&&(h=Q({trace:!1,..."object"==typeof f&&f}));let b=ec(function(...e){return t=>(r,o)=>{let i=t(r,o),a=()=>{throw Error(n(15))},u={getState:i.getState,dispatch:(e,...t)=>a(e,...t)};return a=l(...e.map(e=>e(u)))(i.dispatch),{...i,dispatch:a}}}(...r));return function e(t,r,o){if("function"!=typeof t)throw Error(n(2));if("function"==typeof r&&"function"==typeof o||"function"==typeof o&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof r&&void 0===o&&(o=r,r=void 0),void 0!==o){if("function"!=typeof o)throw Error(n(1));return o(e)(t,r)}let a=t,l=r,s=new Map,f=s,d=0,p=!1;function y(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function h(){if(p)throw Error(n(3));return l}function b(e){if("function"!=typeof e)throw Error(n(4));if(p)throw Error(n(5));let t=!0;y();let r=d++;return f.set(r,e),function(){if(t){if(p)throw Error(n(6));t=!1,y(),f.delete(r),s=null}}}function _(e){if(!c(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(p)throw Error(n(9));try{p=!0,l=a(l,e)}finally{p=!1}return(s=f).forEach(e=>{e()}),e}return _({type:u.INIT}),{dispatch:_,subscribe:b,getState:h,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,_({type:u.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(h())}return t(),{unsubscribe:b(t)}},[i](){return this}}}}}(t,p,h(..."function"==typeof y?y(b):b()))}function es(e){let t,r={},n=[],o={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(eJ(28));if(n in r)throw Error(eJ(29));return r[n]=t,o},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(t=e,o)};return e(o),[r,n,t]}var ef=(e,t)=>Y(e)?e.match(t):e(t);function ed(...e){return t=>e.some(e=>ef(e,t))}var ep=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},ey=["name","message","stack","code"],eh=class{constructor(e,t){this.payload=e,this.meta=t}_type},eb=class{constructor(e,t){this.payload=e,this.meta=t}_type},e_=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of ey)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},ew="External signal was aborted",em=(()=>{function e(e,t,r){let n=ee(e+"/fulfilled",(e,t,r,n)=>({payload:e,meta:{...n||{},arg:r,requestId:t,requestStatus:"fulfilled"}})),o=ee(e+"/pending",(e,t,r)=>({payload:void 0,meta:{...r||{},arg:t,requestId:e,requestStatus:"pending"}})),i=ee(e+"/rejected",(e,t,n,o,i)=>({payload:o,error:(r&&r.serializeError||e_)(e||"Rejected"),meta:{...i||{},arg:n,requestId:t,rejectedWithValue:!!o,requestStatus:"rejected",aborted:e?.name==="AbortError",condition:e?.name==="ConditionError"}}));return Object.assign(function(e,{signal:a}={}){return(u,c,l)=>{let s,f,d=r?.idGenerator?r.idGenerator(e):ep(),p=new AbortController;function y(e){f=e,p.abort()}a&&(a.aborted?y(ew):a.addEventListener("abort",()=>y(ew),{once:!0}));let h=async function(){let a;try{var h;let i=r?.condition?.(e,{getState:c,extra:l});if(h=i,null!==h&&"object"==typeof h&&"function"==typeof h.then&&(i=await i),!1===i||p.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};let b=new Promise((e,t)=>{s=()=>{t({name:"AbortError",message:f||"Aborted"})},p.signal.addEventListener("abort",s)});u(o(d,e,r?.getPendingMeta?.({requestId:d,arg:e},{getState:c,extra:l}))),a=await Promise.race([b,Promise.resolve(t(e,{dispatch:u,getState:c,extra:l,requestId:d,signal:p.signal,abort:y,rejectWithValue:(e,t)=>new eh(e,t),fulfillWithValue:(e,t)=>new eb(e,t)})).then(t=>{if(t instanceof eh)throw t;return t instanceof eb?n(t.payload,d,e,t.meta):n(t,d,e)})])}catch(t){a=t instanceof eh?i(null,d,e,t.payload,t.meta):i(t,d,e)}finally{s&&p.signal.removeEventListener("abort",s)}return r&&!r.dispatchConditionRejection&&i.match(a)&&a.meta.condition||u(a),a}();return Object.assign(h,{abort:y,requestId:d,arg:e,unwrap:()=>h.then(eg)})}},{pending:o,rejected:i,fulfilled:n,settled:ed(i,n),typePrefix:e})}return e.withTypes=()=>e,e})();function eg(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var ev=Symbol.for("rtk-slice-createasyncthunk"),eE=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(eE||{}),eO=function({creators:e}={}){let t=e?.asyncThunk?.[ev];return function(e){let r,{name:n,reducerPath:o=n}=e;if(!n)throw Error(eJ(11));let i=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},a=Object.keys(i),u={},c={},l={},s=[],f={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(eJ(12));if(r in c)throw Error(eJ(13));return c[r]=t,f},addMatcher:(e,t)=>(s.push({matcher:e,reducer:t}),f),exposeAction:(e,t)=>(l[e]=t,f),exposeCaseReducer:(e,t)=>(u[e]=t,f)};function d(){let[t={},r=[],n]="function"==typeof e.extraReducers?es(e.extraReducers):[e.extraReducers],o={...t,...c};return function(e,t){let r,[n,o,i]=es(t);if("function"==typeof e)r=()=>en(e());else{let t=en(e);r=()=>t}function a(e=r(),t){let u=[n[t.type],...o.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===u.filter(e=>!!e).length&&(u=[i]),u.reduce((e,r)=>{if(r)if(_(e)){let n=r(e,t);return void 0===n?e:n}else{if(w(e))return J(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return a.getInitialState=r,a}(e.initialState,e=>{for(let t in o)e.addCase(t,o[t]);for(let t of s)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}a.forEach(r=>{let o=i[r],a={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===o._reducerDefinitionType?function({type:e,reducerName:t},r,n,o){if(!o)throw Error(eJ(18));let{payloadCreator:i,fulfilled:a,pending:u,rejected:c,settled:l,options:s}=r,f=o(e,i,s);n.exposeAction(t,f),a&&n.addCase(f.fulfilled,a),u&&n.addCase(f.pending,u),c&&n.addCase(f.rejected,c),l&&n.addMatcher(f.settled,l),n.exposeCaseReducer(t,{fulfilled:a||eS,pending:u||eS,rejected:c||eS,settled:l||eS})}(a,o,f,t):function({type:e,reducerName:t,createNotation:r},n,o){let i,a;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(eJ(17));i=n.reducer,a=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,a?ee(e,a):ee(e))}(a,o,f)});let p=e=>e,y=new Map,h=new WeakMap;function b(e,t){return r||(r=d()),r(e,t)}function m(){return r||(r=d()),r.getInitialState()}function g(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=eo(h,n,m)),o}function o(t=p){let n=eo(y,r,()=>new WeakMap);return eo(n,t,()=>{let n={};for(let[o,i]of Object.entries(e.selectors??{}))n[o]=function(e,t,r,n){function o(i,...a){let u=t(i);return void 0===u&&n&&(u=r()),e(u,...a)}return o.unwrapped=e,o}(i,t,()=>eo(h,t,m),r);return n})}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}let v={name:n,reducer:b,actions:l,caseReducers:u,getInitialState:m,...g(o),injectInto(e,{reducerPath:t,...r}={}){let n=t??o;return e.inject({reducerPath:n,reducer:b},r),{...v,...g(n,!0)}}};return v}}();function eS(){}function eP(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(et)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function ej(e,t){return t(e)}function ex(e){return Array.isArray(e)||(e=Object.values(e)),e}var eT=class{constructor(e){this.code=e,this.message=`task cancelled (reason: ${e})`}name="TaskAbortError";message},eC=(e,t)=>{if("function"!=typeof e)throw TypeError(eJ(32))},eN=()=>{},eA=(e,t=eN)=>(e.catch(t),e),ek=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),eD=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},eR=e=>{if(e.aborted){let{reason:t}=e;throw new eT(t)}};function eM(e,t){let r=eN;return new Promise((n,o)=>{let i=()=>o(new eT(e.reason));if(e.aborted)return void i();r=ek(e,i),t.finally(()=>r()).then(n,o)}).finally(()=>{r=eN})}var ez=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof eT?"cancelled":"rejected",error:e}}finally{t?.()}},eF=e=>t=>eA(eM(e,t).then(t=>(eR(e),t))),e$=e=>{let t=eF(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:eI}=Object,eW="listenerMiddleware",eL=e=>{let{type:t,actionCreator:r,matcher:n,predicate:o,effect:i}=e;if(t)o=ee(t).match;else if(r)t=r.type,o=r.match;else if(n)o=n;else if(o);else throw Error(eJ(21));return eC(i,"options.listener"),{predicate:o,type:t,effect:i}},eU=eI(e=>{let{type:t,predicate:r,effect:n}=eL(e);return{id:ep(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(eJ(22))}}},{withTypes:()=>eU}),eV=e=>{e.pending.forEach(e=>{eD(e,null)})},eK=eI(ee(`${eW}/add`),{withTypes:()=>eK}),eq=eI(ee(`${eW}/remove`),{withTypes:()=>eq}),eX=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,eB=Symbol.for("rtk-state-proxy-original"),eG=e=>!!e&&!!e[eB],eZ=new WeakMap,eH={};function eJ(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},1992:(e,t,r)=>{e.exports=r(4993)},4540:(e,t,r)=>{r.d(t,{Kq:()=>x,d4:()=>R,wA:()=>k});var n=r(2115),o=r(1992),i=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function u(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var c={notify(){},get:()=>[]},l="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,s="undefined"!=typeof navigator&&"ReactNative"===navigator.product,f=l||s?n.useLayoutEffect:n.useEffect;function d(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var p={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},y={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},h={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},b={[i]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:h};function _(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case i:case null:case a:case null:return e;default:return t}}case null:return t}}}(e)===a?h:b[e.$$typeof]||p}var w=Object.defineProperty,m=Object.getOwnPropertyNames,g=Object.getOwnPropertySymbols,v=Object.getOwnPropertyDescriptor,E=Object.getPrototypeOf,O=Object.prototype,S=Symbol.for("react-redux-context"),P="undefined"!=typeof globalThis?globalThis:{},j=function(){if(!n.createContext)return{};let e=P[S]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),x=function(e){let{children:t,context:r,serverState:o,store:i}=e,a=n.useMemo(()=>{let e=function(e,t){let r,n=c,o=0,i=!1;function a(){s.onStateChange&&s.onStateChange()}function u(){if(o++,!r){let t,o;r=e.subscribe(a),t=null,o=null,n={clear(){t=null,o=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=o={callback:e,next:null,prev:o};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:o=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function l(){o--,r&&0===o&&(r(),r=void 0,n.clear(),n=c)}let s={addNestedSub:function(e){u();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),l())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:a,isSubscribed:function(){return i},trySubscribe:function(){i||(i=!0,u())},tryUnsubscribe:function(){i&&(i=!1,l())},getListeners:()=>n};return s}(i);return{store:i,subscription:e,getServerState:o?()=>o:void 0}},[i,o]),u=n.useMemo(()=>i.getState(),[i]);return f(()=>{let{subscription:e}=a;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[a,u]),n.createElement((r||j).Provider,{value:a},t)};function T(e=j){return function(){return n.useContext(e)}}var C=T();function N(e=j){let t=e===j?C:T(e),r=()=>{let{store:e}=t();return e};return Object.assign(r,{withTypes:()=>r}),r}var A=N(),k=function(e=j){let t=e===j?A:N(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}(),D=(e,t)=>e===t,R=function(e=j){let t=e===j?C:T(e),r=(e,r={})=>{let{equalityFn:i=D}="function"==typeof r?{equalityFn:r}:r,{store:a,subscription:u,getServerState:c}=t();n.useRef(!0);let l=n.useCallback({[e.name]:t=>e(t)}[e.name],[e]),s=(0,o.useSyncExternalStoreWithSelector)(u.addNestedSub,a.getState,c||a.getState,l,i);return n.useDebugValue(s),s};return Object.assign(r,{withTypes:()=>r}),r}()},4993:(e,t,r)=>{var n=r(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useSyncExternalStore,a=n.useRef,u=n.useEffect,c=n.useMemo,l=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,s){var f=a(null);if(null===f.current){var d={hasValue:!1,value:null};f.current=d}else d=f.current;var p=i(e,(f=c(function(){function e(e){if(!u){if(u=!0,i=e,e=n(e),void 0!==s&&d.hasValue){var t=d.value;if(s(t,e))return a=t}return a=e}if(t=a,o(i,e))return t;var r=n(e);return void 0!==s&&s(t,r)?(i=e,t):(i=e,a=r)}var i,a,u=!1,c=void 0===r?null:r;return[function(){return e(t())},null===c?void 0:function(){return e(c())}]},[t,r,n,s]))[0],f[1]);return u(function(){d.hasValue=!0,d.value=p},[p]),l(p),p}}}]);