"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[750],{4001:(e,r,s)=>{s.d(r,{cn:()=>n,vv:()=>o});var t=s(2596),a=s(9688);function n(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,a.QP)((0,t.$)(r))}function o(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD",s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en-US";return new Intl.NumberFormat(s,{style:"currency",currency:r}).format(e)}},9750:(e,r,s)=>{s.d(r,{Fc:()=>d,TN:()=>c,bf:()=>u,XL:()=>i,Ex:()=>m,$n:()=>g,Zp:()=>p,Wu:()=>w,BT:()=>v,wL:()=>N,aR:()=>b,ZB:()=>y,ms:()=>j,pd:()=>C,aF:()=>z,$m:()=>F,jl:()=>E,rQ:()=>L,wt:()=>M,l6:()=>V,lM:()=>W});var t=s(5155),a=s(2085),n=s(2115),o=s(4001);let l=(0,a.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-white border-secondary-200 text-secondary-900",info:"bg-primary-50 border-primary-200 text-primary-900 [&>svg]:text-primary-600",success:"bg-success-50 border-success-200 text-success-900 [&>svg]:text-success-600",warning:"bg-warning-50 border-warning-200 text-warning-900 [&>svg]:text-warning-600",error:"bg-error-50 border-error-200 text-error-900 [&>svg]:text-error-600"}},defaultVariants:{variant:"default"}}),d=n.forwardRef((e,r)=>{let{className:s,variant:a,children:d,dismissible:i,onDismiss:c,icon:u,...f}=e,[m,x]=n.useState(!0);return m?(0,t.jsxs)("div",{ref:r,role:"alert",className:(0,o.cn)(l({variant:a}),s),...f,children:[u,(0,t.jsx)("div",{className:"flex-1",children:d}),i&&(0,t.jsx)("button",{onClick:()=>{x(!1),null==c||c()},className:"absolute right-2 top-2 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2","aria-label":"Close alert",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):null});d.displayName="Alert";let i=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",s),...a})});i.displayName="AlertTitle";let c=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",s),...a})});c.displayName="AlertDescription";let u={info:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),success:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),warning:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),error:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})},f=(0,a.F)("inline-flex items-center rounded-full font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",primary:"bg-primary-100 text-primary-800 hover:bg-primary-200",secondary:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",success:"bg-success-100 text-success-800 hover:bg-success-200",warning:"bg-warning-100 text-warning-800 hover:bg-warning-200",error:"bg-error-100 text-error-800 hover:bg-error-200",outline:"border border-secondary-300 text-secondary-700 hover:bg-secondary-50"},size:{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},dot:{true:"pl-1.5",false:""}},defaultVariants:{variant:"default",size:"md",dot:!1}}),m=n.forwardRef((e,r)=>{let{className:s,variant:a,size:n,dot:l,children:d,dotColor:i,...c}=e;return(0,t.jsxs)("div",{ref:r,className:(0,o.cn)(f({variant:a,size:n,dot:l}),s),...c,children:[l&&(0,t.jsx)("span",{className:(0,o.cn)("mr-1.5 h-1.5 w-1.5 rounded-full",i||"bg-current")}),d]})});m.displayName="Badge";let x=(0,a.F)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{variants:{variant:{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-secondary-300 bg-white text-secondary-700 hover:bg-secondary-50 focus:ring-secondary-500",ghost:"text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500",danger:"bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm",success:"bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm"},size:{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base",xl:"h-14 px-8 text-lg"},fullWidth:{true:"w-full",false:"w-auto"}},defaultVariants:{variant:"primary",size:"md",fullWidth:!1}}),g=n.forwardRef((e,r)=>{let{className:s,variant:a,size:n,fullWidth:l,loading:d,leftIcon:i,rightIcon:c,children:u,disabled:f,...m}=e;return(0,t.jsxs)("button",{className:(0,o.cn)(x({variant:a,size:n,fullWidth:l,className:s})),ref:r,disabled:f||d,...m,children:[d&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!d&&i&&(0,t.jsx)("span",{className:"mr-2",children:i}),u,!d&&c&&(0,t.jsx)("span",{className:"ml-2",children:c})]})});g.displayName="Button";let h=(0,a.F)("rounded-lg bg-white transition-all duration-200",{variants:{variant:{default:"border border-secondary-200 shadow-sm",outlined:"border border-secondary-300",elevated:"shadow-lg border border-secondary-100",ghost:"border-0 shadow-none"},padding:{none:"p-0",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"},hover:{true:"hover:shadow-md hover:border-secondary-300",false:""}},defaultVariants:{variant:"default",padding:"md",hover:!1}}),p=n.forwardRef((e,r)=>{let{className:s,variant:a,padding:n,hover:l,children:d,...i}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)(h({variant:a,padding:n,hover:l}),s),...i,children:d})});p.displayName="Card";let b=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 pb-4",s),...a})});b.displayName="CardHeader";let y=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight text-secondary-900",s),...a})});y.displayName="CardTitle";let v=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-secondary-600",s),...a})});v.displayName="CardDescription";let w=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("",s),...a})});w.displayName="CardContent";let N=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center pt-4",s),...a})});N.displayName="CardFooter";let j=n.forwardRef((e,r)=>{let{trigger:s,items:a,align:l="left",className:d,menuClassName:i}=e,[c,u]=(0,n.useState)(!1),f=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{f.current&&!f.current.contains(e.target)&&u(!1)};return c&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[c]);let m=e=>{!e.disabled&&e.onClick&&e.onClick(),u(!1)};return(0,t.jsxs)("div",{className:(0,o.cn)("relative inline-block",d),ref:f,children:[(0,t.jsx)("div",{onClick:()=>u(!c),className:"cursor-pointer",children:s}),c&&(0,t.jsx)("div",{className:(0,o.cn)("absolute z-50 mt-1 min-w-48 bg-white border border-secondary-300 rounded-lg shadow-lg py-1","right"===l?"right-0":"left-0",i),children:a.map(e=>(0,t.jsxs)("button",{type:"button",className:(0,o.cn)("w-full px-3 py-2 text-left text-sm flex items-center space-x-2 hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none",e.disabled&&"opacity-50 cursor-not-allowed",e.danger&&"text-error-600 hover:bg-error-50 focus:bg-error-50"),onClick:()=>m(e),disabled:e.disabled,children:[e.icon&&(0,t.jsx)("span",{className:"flex-shrink-0",children:e.icon}),"string"==typeof e.label?(0,t.jsx)("span",{children:e.label}):e.label]},e.key))})]})});j.displayName="Dropdown";let k=(0,a.F)("flex w-full rounded-lg border bg-white px-3 py-2 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-secondary-300 focus:border-primary-500 focus:ring-primary-500",error:"border-error-500 focus:border-error-500 focus:ring-error-500",success:"border-success-500 focus:border-success-500 focus:ring-success-500"},size:{sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"}},defaultVariants:{variant:"default",size:"md"}}),C=n.forwardRef((e,r)=>{let{className:s,variant:a,size:n,type:l="text",label:d,helperText:i,error:c,leftIcon:u,rightIcon:f,containerClassName:m,id:x,...g}=e,h=x||"input-".concat(Math.random().toString(36).substr(2,9)),p=!!c;return(0,t.jsxs)("div",{className:(0,o.cn)("space-y-1",m),children:[d&&(0,t.jsxs)("label",{htmlFor:h,className:"block text-sm font-medium text-secondary-700",children:[d,g.required&&(0,t.jsx)("span",{className:"text-error-500 ml-1",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",children:[u&&(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:u}),(0,t.jsx)("input",{type:l,className:(0,o.cn)(k({variant:p?"error":a,size:n}),u&&"pl-10",f&&"pr-10",s),ref:r,id:h,...g}),f&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:f})]}),(c||i)&&(0,t.jsx)("p",{className:(0,o.cn)("text-xs",c?"text-error-600":"text-secondary-500"),children:c||i})]})});C.displayName="Input";let R=(0,a.F)("relative bg-white rounded-lg shadow-xl transform transition-all",{variants:{size:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl",full:"max-w-full mx-4"}},defaultVariants:{size:"md"}}),z=n.forwardRef((e,r)=>{let{className:s,size:a,isOpen:l,onClose:d,children:i,closeOnBackdropClick:c=!0,closeOnEscape:u=!0,showCloseButton:f=!0,...m}=e;return(n.useEffect(()=>{if(!u)return;let e=e=>{"Escape"===e.key&&l&&d()};return l&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[l,d,u]),l)?(0,t.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",onClick:e=>{c&&e.target===e.currentTarget&&d()},children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity"}),(0,t.jsxs)("div",{ref:r,className:(0,o.cn)(R({size:a}),"relative w-full",s),...m,children:[f&&(0,t.jsx)("button",{onClick:d,className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-10","aria-label":"Close modal",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),i]})]})}):null});z.displayName="Modal";let L=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-4",s),...a})});L.displayName="ModalHeader";let M=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("h3",{ref:r,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight text-secondary-900",s),...a})});M.displayName="ModalTitle",n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-secondary-600",s),...a})}).displayName="ModalDescription";let F=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("px-6 py-4",s),...a})});F.displayName="ModalContent";let E=n.forwardRef((e,r)=>{let{className:s,...a}=e;return(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-4",s),...a})});E.displayName="ModalFooter";let B=(0,a.F)("flex w-full items-center justify-between rounded-lg border bg-white px-3 py-2 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-secondary-300 focus:border-primary-500 focus:ring-primary-500",error:"border-error-500 focus:border-error-500 focus:ring-error-500",success:"border-success-500 focus:border-success-500 focus:ring-success-500"},size:{sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"}},defaultVariants:{variant:"default",size:"md"}}),V=n.forwardRef((e,r)=>{let{className:s,variant:a,size:l,options:d,value:i,onChange:c,placeholder:u="Select an option...",label:f,helperText:m,error:x,searchable:g=!1,containerClassName:h,disabled:p,...b}=e,[y,v]=(0,n.useState)(!1),[w,N]=(0,n.useState)(""),j=(0,n.useRef)(null),k=!!x,C=d.find(e=>e.value===i),R=g?d.filter(e=>e.label.toLowerCase().includes(w.toLowerCase())):d;(0,n.useEffect)(()=>{let e=e=>{j.current&&!j.current.contains(e.target)&&(v(!1),N(""))};return y&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[y]);let z=e=>{null==c||c(e),v(!1),N("")};return(0,t.jsxs)("div",{className:(0,o.cn)("relative",h),ref:j,children:[f&&(0,t.jsx)("label",{className:"block text-sm font-medium text-secondary-700 mb-1",children:f}),(0,t.jsxs)("button",{type:"button",ref:r,className:(0,o.cn)(B({variant:k?"error":a,size:l}),s),onClick:()=>!p&&v(!y),disabled:p,...b,children:[(0,t.jsx)("span",{className:(0,o.cn)("truncate",!C&&"text-secondary-500"),children:C?C.label:u}),(0,t.jsx)("svg",{className:(0,o.cn)("h-4 w-4 transition-transform",y&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),y&&(0,t.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-secondary-300 rounded-lg shadow-lg max-h-60 overflow-auto",children:[g&&(0,t.jsx)("div",{className:"p-2 border-b border-secondary-200",children:(0,t.jsx)("input",{type:"text",placeholder:"Search options...",value:w,onChange:e=>N(e.target.value),className:"w-full px-2 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"})}),(0,t.jsx)("div",{className:"py-1",children:0===R.length?(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-secondary-500",children:"No options found"}):R.map(e=>(0,t.jsx)("button",{type:"button",className:(0,o.cn)("w-full px-3 py-2 text-left text-sm hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none",e.value===i&&"bg-primary-50 text-primary-700",e.disabled&&"opacity-50 cursor-not-allowed"),onClick:()=>!e.disabled&&z(e.value),disabled:e.disabled,children:e.label},e.value))})]}),(x||m)&&(0,t.jsx)("p",{className:(0,o.cn)("mt-1 text-xs",x?"text-error-600":"text-secondary-500"),children:x||m})]})});V.displayName="Select";let S=(0,a.F)("peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",{variants:{size:{sm:"h-4 w-7",md:"h-5 w-9",lg:"h-6 w-11"},variant:{default:"data-[state=checked]:bg-primary-600 data-[state=unchecked]:bg-secondary-200",success:"data-[state=checked]:bg-success-600 data-[state=unchecked]:bg-secondary-200",warning:"data-[state=checked]:bg-warning-600 data-[state=unchecked]:bg-secondary-200",error:"data-[state=checked]:bg-error-600 data-[state=unchecked]:bg-secondary-200"}},defaultVariants:{size:"md",variant:"default"}}),T=(0,a.F)("pointer-events-none block rounded-full bg-white shadow-lg ring-0 transition-transform",{variants:{size:{sm:"h-3 w-3 data-[state=checked]:translate-x-3 data-[state=unchecked]:translate-x-0",md:"h-4 w-4 data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0",lg:"h-5 w-5 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"}},defaultVariants:{size:"md"}}),W=n.forwardRef((e,r)=>{let{className:s,size:a,variant:n,checked:l=!1,onChange:d,label:i,description:c,disabled:u,...f}=e,m=()=>{!u&&d&&d(!l)};return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("button",{type:"button",role:"switch","aria-checked":l,"data-state":l?"checked":"unchecked",onClick:m,className:(0,o.cn)(S({size:a,variant:n}),s),disabled:u,ref:r,...f,children:(0,t.jsx)("span",{"data-state":l?"checked":"unchecked",className:(0,o.cn)(T({size:a}))})}),(i||c)&&(0,t.jsxs)("div",{className:"flex flex-col",children:[i&&(0,t.jsx)("label",{className:"text-sm font-medium text-secondary-900 cursor-pointer",onClick:m,children:i}),c&&(0,t.jsx)("p",{className:"text-xs text-secondary-600",children:c})]})]})});W.displayName="Toggle"}}]);