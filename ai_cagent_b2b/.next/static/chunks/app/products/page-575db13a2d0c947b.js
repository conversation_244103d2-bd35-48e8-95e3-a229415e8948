(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[571],{6909:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>F});var a=s(5155),t=s(2115),l=s(2085),n=s(2596),o=s(9688);function i(){for(var e=arguments.length,r=Array(e),s=0;s<e;s++)r[s]=arguments[s];return(0,o.QP)((0,n.$)(r))}let d=(0,l.F)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{variants:{variant:{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-secondary-300 bg-white text-secondary-700 hover:bg-secondary-50 focus:ring-secondary-500",ghost:"text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500",danger:"bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm",success:"bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm"},size:{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base",xl:"h-14 px-8 text-lg"},fullWidth:{true:"w-full",false:"w-auto"}},defaultVariants:{variant:"primary",size:"md",fullWidth:!1}}),c=t.forwardRef((e,r)=>{let{className:s,variant:t,size:l,fullWidth:n,loading:o,leftIcon:c,rightIcon:x,children:m,disabled:u,...f}=e;return(0,a.jsxs)("button",{className:i(d({variant:t,size:l,fullWidth:n,className:s})),ref:r,disabled:u||o,...f,children:[o&&(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!o&&c&&(0,a.jsx)("span",{className:"mr-2",children:c}),m,!o&&x&&(0,a.jsx)("span",{className:"ml-2",children:x})]})});c.displayName="Button";let x=(0,l.F)("flex w-full rounded-lg border bg-white px-3 py-2 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-secondary-300 focus:border-primary-500 focus:ring-primary-500",error:"border-error-500 focus:border-error-500 focus:ring-error-500",success:"border-success-500 focus:border-success-500 focus:ring-success-500"},size:{sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"}},defaultVariants:{variant:"default",size:"md"}}),m=t.forwardRef((e,r)=>{let{className:s,variant:t,size:l,type:n="text",label:o,helperText:d,error:c,leftIcon:m,rightIcon:u,containerClassName:f,id:h,...g}=e,p=h||"input-".concat(Math.random().toString(36).substr(2,9)),v=!!c;return(0,a.jsxs)("div",{className:i("space-y-1",f),children:[o&&(0,a.jsxs)("label",{htmlFor:p,className:"block text-sm font-medium text-secondary-700",children:[o,g.required&&(0,a.jsx)("span",{className:"text-error-500 ml-1",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[m&&(0,a.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:m}),(0,a.jsx)("input",{type:n,className:i(x({variant:v?"error":t,size:l}),m&&"pl-10",u&&"pr-10",s),ref:r,id:p,...g}),u&&(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:u})]}),(c||d)&&(0,a.jsx)("p",{className:i("text-xs",c?"text-error-600":"text-secondary-500"),children:c||d})]})});m.displayName="Input";let u=(0,l.F)("rounded-lg bg-white transition-all duration-200",{variants:{variant:{default:"border border-secondary-200 shadow-sm",outlined:"border border-secondary-300",elevated:"shadow-lg border border-secondary-100",ghost:"border-0 shadow-none"},padding:{none:"p-0",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"},hover:{true:"hover:shadow-md hover:border-secondary-300",false:""}},defaultVariants:{variant:"default",padding:"md",hover:!1}}),f=t.forwardRef((e,r)=>{let{className:s,variant:t,padding:l,hover:n,children:o,...d}=e;return(0,a.jsx)("div",{ref:r,className:i(u({variant:t,padding:l,hover:n}),s),...d,children:o})});f.displayName="Card";let h=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("flex flex-col space-y-1.5 pb-4",s),...t})});h.displayName="CardHeader";let g=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("h3",{ref:r,className:i("text-lg font-semibold leading-none tracking-tight text-secondary-900",s),...t})});g.displayName="CardTitle";let p=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("p",{ref:r,className:i("text-sm text-secondary-600",s),...t})});p.displayName="CardDescription";let v=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("",s),...t})});v.displayName="CardContent";let y=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("flex items-center pt-4",s),...t})});y.displayName="CardFooter";let b=(0,l.F)("inline-flex items-center rounded-full font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",primary:"bg-primary-100 text-primary-800 hover:bg-primary-200",secondary:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",success:"bg-success-100 text-success-800 hover:bg-success-200",warning:"bg-warning-100 text-warning-800 hover:bg-warning-200",error:"bg-error-100 text-error-800 hover:bg-error-200",outline:"border border-secondary-300 text-secondary-700 hover:bg-secondary-50"},size:{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},dot:{true:"pl-1.5",false:""}},defaultVariants:{variant:"default",size:"md",dot:!1}}),j=t.forwardRef((e,r)=>{let{className:s,variant:t,size:l,dot:n,children:o,dotColor:d,...c}=e;return(0,a.jsxs)("div",{ref:r,className:i(b({variant:t,size:l,dot:n}),s),...c,children:[n&&(0,a.jsx)("span",{className:i("mr-1.5 h-1.5 w-1.5 rounded-full",d||"bg-current")}),o]})});j.displayName="Badge";let N=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-white border-secondary-200 text-secondary-900",info:"bg-primary-50 border-primary-200 text-primary-900 [&>svg]:text-primary-600",success:"bg-success-50 border-success-200 text-success-900 [&>svg]:text-success-600",warning:"bg-warning-50 border-warning-200 text-warning-900 [&>svg]:text-warning-600",error:"bg-error-50 border-error-200 text-error-900 [&>svg]:text-error-600"}},defaultVariants:{variant:"default"}}),w=t.forwardRef((e,r)=>{let{className:s,variant:l,children:n,dismissible:o,onDismiss:d,icon:c,...x}=e,[m,u]=t.useState(!0);return m?(0,a.jsxs)("div",{ref:r,role:"alert",className:i(N({variant:l}),s),...x,children:[c,(0,a.jsx)("div",{className:"flex-1",children:n}),o&&(0,a.jsx)("button",{onClick:()=>{u(!1),null==d||d()},className:"absolute right-2 top-2 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2","aria-label":"Close alert",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):null});w.displayName="Alert";let k=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("h5",{ref:r,className:i("mb-1 font-medium leading-none tracking-tight",s),...t})});k.displayName="AlertTitle";let C=t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("text-sm [&_p]:leading-relaxed",s),...t})});C.displayName="AlertDescription";let R={info:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),success:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),warning:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),error:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})},z=(0,l.F)("relative bg-white rounded-lg shadow-xl transform transition-all",{variants:{size:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl",full:"max-w-full mx-4"}},defaultVariants:{size:"md"}});t.forwardRef((e,r)=>{let{className:s,size:l,isOpen:n,onClose:o,children:d,closeOnBackdropClick:c=!0,closeOnEscape:x=!0,showCloseButton:m=!0,...u}=e;return(t.useEffect(()=>{if(!x)return;let e=e=>{"Escape"===e.key&&n&&o()};return n&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[n,o,x]),n)?(0,a.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,a.jsxs)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",onClick:e=>{c&&e.target===e.currentTarget&&o()},children:[(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity"}),(0,a.jsxs)("div",{ref:r,className:i(z({size:l}),"relative w-full",s),...u,children:[m&&(0,a.jsx)("button",{onClick:o,className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-10","aria-label":"Close modal",children:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),d]})]})}):null}).displayName="Modal",t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-4",s),...t})}).displayName="ModalHeader",t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("h3",{ref:r,className:i("text-lg font-semibold leading-none tracking-tight text-secondary-900",s),...t})}).displayName="ModalTitle",t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("p",{ref:r,className:i("text-sm text-secondary-600",s),...t})}).displayName="ModalDescription",t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("px-6 py-4",s),...t})}).displayName="ModalContent",t.forwardRef((e,r)=>{let{className:s,...t}=e;return(0,a.jsx)("div",{ref:r,className:i("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-4",s),...t})}).displayName="ModalFooter";var M=s(7680),L=s(9168),D=s(7582);function F(){let e=(0,M.jL)(),{filteredItems:r,loading:s,searchQuery:l}=(0,M.GV)(e=>e.products),[n,o]=(0,t.useState)(!1);(0,t.useEffect)(()=>{e((0,L.j0)())},[e]);let i=r=>{e((0,D.bE)({product:r})),o(!0),setTimeout(()=>o(!1),3e3)},d=r=>{e((0,L.Ri)(r))};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Product Catalog"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Discover our latest collection of premium electronics and technology products."}),n&&(0,a.jsxs)(w,{variant:"success",dismissible:!0,onDismiss:()=>o(!1),icon:R.success,className:"mb-6",children:[(0,a.jsx)(k,{children:"Product Added!"}),(0,a.jsx)(C,{children:"The product has been successfully added to your cart."})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,a.jsx)(m,{placeholder:"Search products...",value:l,onChange:e=>d(e.target.value),className:"sm:max-w-sm",leftIcon:(0,a.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,a.jsx)(c,{variant:"outline",children:"Filter"}),(0,a.jsx)(c,{variant:"outline",children:"Sort"})]})]}),s?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading products..."})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,a.jsxs)(f,{hover:!0,className:"h-full flex flex-col",children:[(0,a.jsxs)(h,{children:[(0,a.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-6xl",children:"\uD83D\uDCF1"})}),(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)(g,{className:"text-lg",children:e.name}),(0,a.jsx)(j,{variant:"primary",children:e.category.name})]}),(0,a.jsx)(p,{className:"line-clamp-2",children:e.description})]}),(0,a.jsx)(v,{className:"flex-1",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"SKU:"}),(0,a.jsx)("span",{className:"text-sm font-mono",children:e.sku})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"Stock:"}),(0,a.jsx)(j,{variant:e.stock>10?"success":e.stock>0?"warning":"error",children:e.stock>0?"".concat(e.stock," available"):"Out of stock"})]})]})}),(0,a.jsxs)(y,{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:["$",e.price.toLocaleString()]}),(0,a.jsx)(c,{onClick:()=>i(e),disabled:0===e.stock,size:"sm",children:0===e.stock?"Out of Stock":"Add to Cart"})]})]},e.id))}),!s&&0===r.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filter criteria."})]})]})})}},8598:(e,r,s)=>{Promise.resolve().then(s.bind(s,6909))}},e=>{var r=r=>e(e.s=r);e.O(0,[635,3,680,441,684,358],()=>r(8598)),_N_E=e.O()}]);