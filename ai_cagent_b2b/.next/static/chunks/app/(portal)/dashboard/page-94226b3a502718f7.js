(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[589],{6491:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>r});var a=l(5155),i=l(2115),n=l(9750);function r(){let[e,s]=(0,i.useState)(!1),[l,r]=(0,i.useState)(!1),[c,d]=(0,i.useState)(""),t=[{value:"option1",label:"Option 1"},{value:"option2",label:"Option 2"},{value:"option3",label:"Option 3"}];return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"UI Components Dashboard"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Showcase of all available UI components in the ElectroShop design system."})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Alerts"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.Fc,{variant:"info",icon:n.bf.info,children:[(0,a.jsx)(n.XL,{children:"Information"}),(0,a.jsx)(n.TN,{children:"This is an informational alert message."})]}),(0,a.jsxs)(n.Fc,{variant:"success",icon:n.bf.success,children:[(0,a.jsx)(n.XL,{children:"Success"}),(0,a.jsx)(n.TN,{children:"Your action was completed successfully!"})]}),(0,a.jsxs)(n.Fc,{variant:"warning",icon:n.bf.warning,children:[(0,a.jsx)(n.XL,{children:"Warning"}),(0,a.jsx)(n.TN,{children:"Please review your settings before proceeding."})]}),(0,a.jsxs)(n.Fc,{variant:"error",icon:n.bf.error,dismissible:!0,children:[(0,a.jsx)(n.XL,{children:"Error"}),(0,a.jsx)(n.TN,{children:"Something went wrong. Please try again."})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Buttons"}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)(n.$n,{variant:"primary",children:"Primary"}),(0,a.jsx)(n.$n,{variant:"secondary",children:"Secondary"}),(0,a.jsx)(n.$n,{variant:"outline",children:"Outline"}),(0,a.jsx)(n.$n,{variant:"ghost",children:"Ghost"}),(0,a.jsx)(n.$n,{variant:"danger",children:"Danger"}),(0,a.jsx)(n.$n,{variant:"success",children:"Success"}),(0,a.jsx)(n.$n,{loading:!0,children:"Loading"}),(0,a.jsx)(n.$n,{disabled:!0,children:"Disabled"}),(0,a.jsx)(n.$n,{size:"sm",children:"Small"}),(0,a.jsx)(n.$n,{size:"lg",children:"Large"}),(0,a.jsx)(n.$n,{leftIcon:"\uD83D\uDE80",children:"With Icon"})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Form Components"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Input Components"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsx)(n.pd,{label:"Email",type:"email",placeholder:"Enter your email"}),(0,a.jsx)(n.pd,{label:"Password",type:"password",placeholder:"Enter password",helperText:"Must be at least 8 characters"}),(0,a.jsx)(n.pd,{label:"Search",placeholder:"Search products...",leftIcon:"\uD83D\uDD0D"}),(0,a.jsx)(n.pd,{label:"Amount",type:"number",placeholder:"0.00",rightIcon:"\uD83D\uDCB0"}),(0,a.jsx)(n.pd,{label:"Error Example",error:"This field is required",placeholder:"Required field"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Select & Toggle"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsx)(n.l6,{label:"Choose Option",options:t,value:c,onChange:d,placeholder:"Select an option..."}),(0,a.jsx)(n.l6,{label:"Searchable Select",options:t,searchable:!0,placeholder:"Search options..."}),(0,a.jsx)(n.lM,{checked:l,onChange:r,label:"Enable notifications",description:"Receive email notifications for updates"}),(0,a.jsx)(n.lM,{checked:!0,label:"Premium features",variant:"success"})]})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Cards & Badges"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)(n.Zp,{hover:!0,children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsx)(n.ZB,{children:"Product Card"}),(0,a.jsx)(n.Ex,{variant:"success",children:"In Stock"})]}),(0,a.jsx)(n.BT,{children:"This is a sample product card with hover effects."})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:"$299.99"})}),(0,a.jsx)(n.wL,{children:(0,a.jsx)(n.$n,{fullWidth:!0,children:"Add to Cart"})})]}),(0,a.jsxs)(n.Zp,{variant:"outlined",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Outlined Card"}),(0,a.jsx)(n.BT,{children:"Card with outlined variant styling."})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,a.jsx)(n.Ex,{variant:"primary",children:"Primary"}),(0,a.jsx)(n.Ex,{variant:"secondary",children:"Secondary"}),(0,a.jsx)(n.Ex,{variant:"success",children:"Success"}),(0,a.jsx)(n.Ex,{variant:"warning",children:"Warning"}),(0,a.jsx)(n.Ex,{variant:"error",children:"Error"})]})})]}),(0,a.jsxs)(n.Zp,{variant:"elevated",children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Elevated Card"}),(0,a.jsx)(n.BT,{children:"Card with elevated shadow styling."})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.Ex,{size:"sm",dot:!0,children:"Small Badge"}),(0,a.jsx)(n.Ex,{size:"md",dot:!0,children:"Medium Badge"}),(0,a.jsx)(n.Ex,{size:"lg",dot:!0,children:"Large Badge"})]})})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Interactive Components"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(n.$n,{onClick:()=>s(!0),children:"Open Modal"}),(0,a.jsx)(n.ms,{trigger:(0,a.jsx)(n.$n,{variant:"outline",children:"Dropdown Menu"}),items:[{key:"edit",label:"Edit",icon:"✏️"},{key:"duplicate",label:"Duplicate",icon:"\uD83D\uDCCB"},{key:"delete",label:"Delete",icon:"\uD83D\uDDD1️",danger:!0}]})]})]}),(0,a.jsxs)(n.aF,{isOpen:e,onClose:()=>s(!1),children:[(0,a.jsx)(n.rQ,{children:(0,a.jsx)(n.wt,{children:"Sample Modal"})}),(0,a.jsx)(n.$m,{children:(0,a.jsx)("p",{className:"text-gray-600",children:"This is a sample modal dialog. You can put any content here including forms, images, or other components."})}),(0,a.jsxs)(n.jl,{children:[(0,a.jsx)(n.$n,{variant:"outline",onClick:()=>s(!1),children:"Cancel"}),(0,a.jsx)(n.$n,{onClick:()=>s(!1),children:"Confirm"})]})]})]})})}},9334:(e,s,l)=>{Promise.resolve().then(l.bind(l,6491))}},e=>{var s=s=>e(e.s=s);e.O(0,[3,750,441,684,358],()=>s(9334)),_N_E=e.O()}]);