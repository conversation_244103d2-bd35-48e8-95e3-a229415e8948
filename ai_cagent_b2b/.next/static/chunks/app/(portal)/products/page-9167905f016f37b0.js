(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[815],{3298:(e,s,a)=>{Promise.resolve().then(a.bind(a,8957))},8957:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d});var t=a(5155),c=a(2115),l=a(9750),r=a(7582),i=a(9168),n=a(7680);function d(){let e=(0,n.jL)(),{filteredItems:s,loading:a,searchQuery:d}=(0,n.GV)(e=>e.products),[o,x]=(0,c.useState)(!1);(0,c.useEffect)(()=>{e((0,i.j0)())},[e]);let m=s=>{e((0,r.bE)({product:s})),x(!0),setTimeout(()=>x(!1),3e3)},h=s=>{e((0,i.Ri)(s))};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,t.jsxs)("div",{className:"mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Product Catalog"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Discover our latest collection of premium electronics and technology products."}),o&&(0,t.jsxs)(l.Fc,{variant:"success",dismissible:!0,onDismiss:()=>x(!1),icon:l.bf.success,className:"mb-6",children:[(0,t.jsx)(l.XL,{children:"Product Added!"}),(0,t.jsx)(l.TN,{children:"The product has been successfully added to your cart."})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,t.jsx)(l.pd,{placeholder:"Search products...",value:d,onChange:e=>h(e.target.value),className:"sm:max-w-sm",leftIcon:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,t.jsx)(l.$n,{variant:"outline",children:"Filter"}),(0,t.jsx)(l.$n,{variant:"outline",children:"Sort"})]})]}),a?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading products..."})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:s.map(e=>(0,t.jsxs)(l.Zp,{hover:!0,className:"h-full flex flex-col",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-6xl",children:"\uD83D\uDCF1"})}),(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)(l.ZB,{className:"text-lg",children:e.name}),(0,t.jsx)(l.Ex,{variant:"primary",children:e.category.name})]}),(0,t.jsx)(l.BT,{className:"line-clamp-2",children:e.description})]}),(0,t.jsx)(l.Wu,{className:"flex-1",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"SKU:"}),(0,t.jsx)("span",{className:"text-sm font-mono",children:e.sku})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-500",children:"Stock:"}),(0,t.jsx)(l.Ex,{variant:e.stock>10?"success":e.stock>0?"warning":"error",children:e.stock>0?"".concat(e.stock," available"):"Out of stock"})]})]})}),(0,t.jsxs)(l.wL,{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:["$",e.price.toLocaleString()]}),(0,t.jsx)(l.$n,{onClick:()=>m(e),disabled:0===e.stock,size:"sm",children:0===e.stock?"Out of Stock":"Add to Cart"})]})]},e.id))}),!a&&0===s.length&&(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filter criteria."})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[635,3,680,750,441,684,358],()=>s(3298)),_N_E=e.O()}]);