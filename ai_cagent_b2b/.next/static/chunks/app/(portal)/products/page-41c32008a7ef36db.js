(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[815],{685:(e,t,s)=>{var r={"./ar/common.json":[8880,880],"./ar/orders.json":[6504,504],"./ar/products.json":[1033,33],"./en/common.json":[5520,520],"./en/orders.json":[792,411],"./en/products.json":[2217,217]};function n(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return s.e(t[1]).then(()=>s.t(n,19))}n.keys=()=>Object.keys(r),n.id=685,e.exports=n},771:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(5155),n=s(2115),a=s(9750),l=s(9283);let c={};function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"common",{locale:t,isRTL:r}=(0,l.o)(),[a,c]=(0,n.useState)({}),[o,i]=(0,n.useState)(!0);return(0,n.useEffect)(()=>{let r=!0;return(async()=>{i(!0);try{let n=await s(685)("./".concat(t,"/").concat(e,".json"));r&&c(n.default||n)}catch(n){if(console.warn("Failed to load translations for ".concat(t,"/").concat(e,":"),n),"en"!==t)try{let t=await s(2233)("./".concat(e,".json"));r&&c(t.default||t)}catch(e){console.error("Failed to load fallback translations:",e),r&&c({})}else r&&c({})}finally{r&&i(!1)}})(),()=>{r=!1}},[t,e]),{t:(e,t)=>(function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(e,t){let s=t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:void 0,e);return"string"==typeof s?s:void 0}(t,e);return"string"==typeof r?function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e.replace(/\{(\w+)\}/g,(e,s)=>{var r;return(null==(r=t[s])?void 0:r.toString())||e})}(r,s):e})(e,a,t),locale:t,isRTL:r,formatCurrency:(e,s)=>new Intl.NumberFormat(t,{style:"currency",currency:s}).format(e),formatDate:(e,s)=>(function(e,t,s){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat(t,s||{year:"numeric",month:"long",day:"numeric"}).format(r)})(e,t,s),formatNumber:(e,s)=>new Intl.NumberFormat(t,s).format(e),pluralize:(e,s,r)=>(function(e,t,s){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"en";return"ar"!==r?1===e?t:s||t+"s":0===e?s||t:1===e?t:(2===e,s||t)})(e,s,r,t),loading:o}}var i=s(7582),d=s(9168),u=s(7680);function m(){let e=(0,u.jL)(),{filteredItems:t,loading:s,searchQuery:l}=(0,u.GV)(e=>e.products),[c,m]=(0,n.useState)(!1),{t:x}=o("products"),{t:h}=o("common");(0,n.useEffect)(()=>{e((0,d.j0)())},[e]);let f=t=>{e((0,i.bE)({product:t})),m(!0),setTimeout(()=>m(!1),3e3)},j=t=>{e((0,d.Ri)(t))};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:x("title")}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:x("subtitle")}),c&&(0,r.jsxs)(a.Fc,{variant:"success",dismissible:!0,onDismiss:()=>m(!1),icon:a.bf.success,className:"mb-6",children:[(0,r.jsx)(a.XL,{children:h("success")}),(0,r.jsx)(a.TN,{children:x("cart.addedToCart")})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,r.jsx)(a.pd,{placeholder:x("search.placeholder"),value:l,onChange:e=>j(e.target.value),className:"sm:max-w-sm",leftIcon:(0,r.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,r.jsx)(a.$n,{variant:"outline",children:x("filters.title")}),(0,r.jsx)(a.$n,{variant:"outline",children:x("sort.title")})]})]}),s?(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:h("loading")})]}):(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,r.jsxs)(a.Zp,{hover:!0,className:"h-full flex flex-col",children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-6xl",children:"\uD83D\uDCF1"})}),(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)(a.ZB,{className:"text-lg",children:e.name}),(0,r.jsx)(a.Ex,{variant:"primary",children:x("categories.".concat(e.category.id))||e.category.name})]}),(0,r.jsx)(a.BT,{className:"line-clamp-2",children:e.description})]}),(0,r.jsx)(a.Wu,{className:"flex-1",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[x("product.sku"),":"]}),(0,r.jsx)("span",{className:"text-sm font-mono",children:e.sku})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-500",children:[x("product.stock"),":"]}),(0,r.jsx)(a.Ex,{variant:e.stock>10?"success":e.stock>0?"warning":"error",children:e.stock>0?"".concat(e.stock," ").concat(x("product.inStock")):x("product.outOfStock")})]})]})}),(0,r.jsxs)(a.wL,{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:["$",e.price.toLocaleString()]}),(0,r.jsx)(a.$n,{onClick:()=>f(e),disabled:0===e.stock,size:"sm",children:0===e.stock?x("product.outOfStock"):x("product.addToCart")})]})]},e.id))}),!s&&0===t.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:x("search.noResults")}),(0,r.jsx)("p",{className:"text-gray-600",children:x("search.noResults")})]})]})})}},2233:(e,t,s)=>{var r={"./common.json":[5520,520],"./orders.json":[792,411],"./products.json":[2217,217]};function n(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return s.e(t[1]).then(()=>s.t(n,19))}n.keys=()=>Object.keys(r),n.id=2233,e.exports=n},3298:(e,t,s)=>{Promise.resolve().then(s.bind(s,771))},9283:(e,t,s)=>{"use strict";s.d(t,{I:()=>c,o:()=>o});var r=s(5155),n=s(2115);let a=(0,n.createContext)(void 0),l=["ar"];function c(e){let{children:t}=e,[s,c]=(0,n.useState)("en");(0,n.useEffect)(()=>{let e=localStorage.getItem("locale");if(e&&["en","ar"].includes(e))c(e);else{let e=navigator.language.split("-")[0];["en","ar"].includes(e)&&c(e)}},[]);let o=l.includes(s),i=o?"rtl":"ltr";(0,n.useEffect)(()=>{document.documentElement.dir=i,document.documentElement.lang=s},[s,i]);let d=e=>{c(e),localStorage.setItem("locale",e)};return(0,r.jsx)(a.Provider,{value:{locale:s,setLocale:d,isRTL:o,direction:i,toggleLanguage:()=>{d("en"===s?"ar":"en")}},children:t})}function o(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}}},e=>{var t=t=>e(e.s=t);e.O(0,[635,3,680,750,441,684,358],()=>t(3298)),_N_E=e.O()}]);