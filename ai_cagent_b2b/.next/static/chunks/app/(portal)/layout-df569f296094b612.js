(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[821],{394:(e,s,r)=>{"use strict";r.d(s,{PortalLayoutContent:()=>P});var t=r(5155),a=r(2115),l=r(9283),n=r(4001),c=r(6874),i=r.n(c),o=r(8999);let d={"":"Home",dashboard:"Dashboard",products:"Products",orders:"Orders",contracts:"Contracts",profile:"Profile",settings:"Settings",help:"Help"},h={"":"\uD83C\uDFE0",dashboard:"\uD83D\uDCCA",products:"\uD83D\uDCF1",orders:"\uD83D\uDCCB",contracts:"\uD83D\uDCC4",profile:"\uD83D\uDC64",settings:"⚙️",help:"❓"};function x(){let e=(0,o.usePathname)(),{isRTL:s}=(0,l.o)(),r=(()=>{let s=e.split("/").filter(Boolean),r=[];r.push({label:d[""]||"Home",href:"/",icon:h[""]});let t="";return s.forEach((e,s)=>{t+="/".concat(e);let a=decodeURIComponent(e),l=d[a]||a.charAt(0).toUpperCase()+a.slice(1).replace(/-/g," ");r.push({label:l,href:t,icon:h[a]})}),r})();return r.length<=1?null:(0,t.jsx)("nav",{className:"flex items-center space-x-1 text-sm text-secondary-600 mb-6","aria-label":"Breadcrumb",children:(0,t.jsx)("ol",{className:"flex items-center space-x-1",children:r.map((e,a)=>{let l=a===r.length-1;return(0,t.jsxs)("li",{className:"flex items-center",children:[a>0&&(0,t.jsx)("svg",{className:(0,n.cn)("h-4 w-4 mx-2 text-secondary-400",s&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),l?(0,t.jsxs)("span",{className:"flex items-center space-x-1 text-secondary-900 font-medium",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]}):(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-1 hover:text-secondary-900 transition-colors",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]})]},e.href)})})})}var m=r(9750),u=r(7582),p=r(7680);function f(){let e=(0,p.jL)(),{items:s,totalItems:r,totalAmount:a,currency:l}=(0,p.GV)(e=>e.cart),c=s=>{e((0,u.dt)(s))},i=(s,r)=>{e((0,u.RO)({itemId:s,quantity:r}))},o=[...s.map(e=>({key:e.id,label:(0,t.jsx)("div",{className:"w-80 p-2",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-secondary-100 rounded flex items-center justify-center",children:"\uD83D\uDCF1"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-secondary-900 truncate",children:e.product.name}),(0,t.jsx)("div",{className:"text-xs text-secondary-500",children:(0,n.vv)(e.product.price,l)}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),i(e.id,Math.max(1,e.quantity-1))},className:"w-6 h-6 rounded border border-secondary-300 flex items-center justify-center text-xs hover:bg-secondary-50",children:"-"}),(0,t.jsx)("span",{className:"text-xs font-medium w-8 text-center",children:e.quantity}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),i(e.id,e.quantity+1)},className:"w-6 h-6 rounded border border-secondary-300 flex items-center justify-center text-xs hover:bg-secondary-50",children:"+"}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),c(e.id)},className:"text-error-600 hover:text-error-700 text-xs ml-2",children:"Remove"})]})]})]})}),onClick:()=>{}})),...s.length>0?[{key:"divider",label:(0,t.jsx)("div",{className:"border-t border-secondary-200 my-2"}),onClick:()=>{}},{key:"total",label:(0,t.jsx)("div",{className:"p-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center font-medium",children:[(0,t.jsx)("span",{children:"Total:"}),(0,t.jsx)("span",{children:(0,n.vv)(a,l)})]})}),onClick:()=>{}},{key:"checkout",label:(0,t.jsx)("div",{className:"p-2",children:(0,t.jsx)(m.$n,{fullWidth:!0,size:"sm",children:"Checkout"})}),onClick:()=>{window.location.href="/checkout"}}]:[{key:"empty",label:(0,t.jsx)("div",{className:"p-4 text-center text-secondary-500",children:"Your cart is empty"}),onClick:()=>{}}]];return(0,t.jsx)(m.ms,{trigger:(0,t.jsxs)(m.$n,{variant:"ghost",size:"sm",className:"relative",children:[(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4"})}),r>0&&(0,t.jsx)(m.Ex,{variant:"error",size:"sm",className:"absolute -top-1 -right-1 min-w-5 h-5 flex items-center justify-center text-xs",children:r})]}),items:o,align:"right",menuClassName:"max-h-96 overflow-y-auto"})}let j=[{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"ar",name:"العربية",flag:"\uD83C\uDDF8\uD83C\uDDE6"}];function v(){let{locale:e,setLocale:s}=(0,l.o)(),r=j.find(s=>s.code===e)||j[0],a=j.map(e=>({key:e.code,label:"".concat(e.flag," ").concat(e.name),onClick:()=>s(e.code)}));return(0,t.jsx)(m.ms,{trigger:(0,t.jsxs)(m.$n,{variant:"ghost",size:"sm",className:"gap-2",children:[(0,t.jsx)("span",{children:r.flag}),(0,t.jsx)("span",{className:"hidden sm:inline",children:r.name}),(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),items:a,align:"right"})}function y(){let[e,s]=(0,a.useState)(""),[r,l]=(0,a.useState)(!1),[n,c]=(0,a.useState)([]),i=(0,a.useRef)(null),{items:o}=(0,p.GV)(e=>e.products);(0,a.useEffect)(()=>{let e=e=>{i.current&&!i.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,a.useEffect)(()=>{if(e.length>1){let s=o.filter(s=>s.name.toLowerCase().includes(e.toLowerCase())||s.description.toLowerCase().includes(e.toLowerCase())).slice(0,5).map(e=>({id:e.id,title:e.name,type:"product",price:e.price}));c(s),l(s.length>0)}else c([]),l(!1)},[e,o]);let d=e=>{s(e)},h=e=>{s(e.title),l(!1),"product"===e.type&&(window.location.href="/products/".concat(e.id))};return(0,t.jsxs)("div",{className:"relative w-full max-w-md",ref:i,children:[(0,t.jsx)(m.pd,{placeholder:"Search products...",value:e,onChange:e=>d(e.target.value),leftIcon:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),className:"pr-10"}),e&&(0,t.jsx)("button",{onClick:()=>{s(""),l(!1)},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),r&&n.length>0&&(0,t.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-secondary-300 rounded-lg shadow-lg max-h-80 overflow-auto",children:[(0,t.jsx)("div",{className:"py-2",children:n.map(e=>(0,t.jsx)("button",{onClick:()=>h(e),className:"w-full px-4 py-3 text-left hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-secondary-100 rounded flex items-center justify-center",children:"product"===e.type?"\uD83D\uDCF1":"\uD83D\uDCC1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-secondary-900",children:e.title}),(0,t.jsx)("div",{className:"text-xs text-secondary-500 capitalize",children:e.type})]})]}),e.price&&(0,t.jsxs)("div",{className:"text-sm font-medium text-primary-600",children:["$",e.price.toLocaleString()]})]})},e.id))}),e&&(0,t.jsx)("div",{className:"border-t border-secondary-200 px-4 py-2",children:(0,t.jsxs)("button",{onClick:()=>{window.location.href="/products?search=".concat(encodeURIComponent(e)),l(!1)},className:"text-sm text-primary-600 hover:text-primary-700",children:['Search for "',e,'"']})})]})]})}var g=r(7740);let N={light:"☀️",dark:"\uD83C\uDF19",system:"\uD83D\uDCBB"},b={light:"Light",dark:"Dark",system:"System"};function k(){let{theme:e,toggleTheme:s}=(0,g.D)();return(0,t.jsxs)(m.$n,{variant:"ghost",size:"sm",onClick:s,className:"gap-2",title:"Current theme: ".concat(b[e]),children:[(0,t.jsx)("span",{children:N[e]}),(0,t.jsx)("span",{className:"hidden sm:inline",children:b[e]})]})}let w=[{href:"/products",label:"Products",icon:"\uD83D\uDCF1"},{href:"/orders",label:"Orders",icon:"\uD83D\uDCCB"},{href:"/contracts",label:"Contracts",icon:"\uD83D\uDCC4"},{href:"/dashboard",label:"Dashboard",icon:"\uD83D\uDCCA"}],C=[{key:"profile",label:"My Profile",icon:"\uD83D\uDC64"},{key:"settings",label:"Settings",icon:"⚙️"},{key:"help",label:"Help & Support",icon:"❓"},{key:"logout",label:"Sign Out",icon:"\uD83D\uDEAA",danger:!0}];function L(){let[e,s]=(0,a.useState)(!1),{isRTL:r}=(0,l.o)();return(0,t.jsxs)("header",{className:"bg-white shadow-sm border-b border-secondary-200 sticky top-0 z-40",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"E"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-secondary-900 hidden sm:block",children:"ElectroShop"})]})}),(0,t.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:w.map(e=>(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-2 text-secondary-600 hover:text-secondary-900 transition-colors",children:[(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.href))}),(0,t.jsx)("div",{className:"flex-1 max-w-lg mx-8 hidden lg:block",children:(0,t.jsx)(y,{})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.$n,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>{s(!e)},children:(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,t.jsx)(k,{}),(0,t.jsx)(v,{}),(0,t.jsx)(f,{}),(0,t.jsx)(m.ms,{trigger:(0,t.jsx)(m.$n,{variant:"ghost",size:"sm",className:"relative",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-primary-600 font-medium",children:"U"})})}),items:C,align:"right"}),(0,t.jsx)(m.$n,{variant:"ghost",size:"sm",className:"md:hidden",onClick:()=>s(!e),children:(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]})]}),(0,t.jsx)("div",{className:"lg:hidden pb-4",children:(0,t.jsx)(y,{})})]}),e&&(0,t.jsx)("div",{className:"md:hidden bg-white border-t border-secondary-200",children:(0,t.jsx)("div",{className:"px-4 py-2 space-y-1",children:w.map(e=>(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50 transition-colors",onClick:()=>s(!1),children:[(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.href))})})]})}let S=[{id:"dashboard",label:"Dashboard",href:"/dashboard",icon:"\uD83D\uDCCA"},{id:"products",label:"Products",href:"/products",icon:"\uD83D\uDCF1",children:[{id:"all-products",label:"All Products",href:"/products",icon:"\uD83D\uDCE6"},{id:"smartphones",label:"Smartphones",href:"/products?category=smartphones",icon:"\uD83D\uDCF1"},{id:"laptops",label:"Laptops",href:"/products?category=laptops",icon:"\uD83D\uDCBB"},{id:"tablets",label:"Tablets",href:"/products?category=tablets",icon:"\uD83D\uDCF1"},{id:"accessories",label:"Accessories",href:"/products?category=accessories",icon:"\uD83C\uDFA7"}]},{id:"orders",label:"Orders",href:"/orders",icon:"\uD83D\uDCCB",badge:"3",children:[{id:"all-orders",label:"All Orders",href:"/orders",icon:"\uD83D\uDCCB"},{id:"pending",label:"Pending",href:"/orders?status=pending",icon:"⏳"},{id:"processing",label:"Processing",href:"/orders?status=processing",icon:"⚙️"},{id:"shipped",label:"Shipped",href:"/orders?status=shipped",icon:"\uD83D\uDE9A"},{id:"delivered",label:"Delivered",href:"/orders?status=delivered",icon:"✅"}]},{id:"contracts",label:"Contracts",href:"/contracts",icon:"\uD83D\uDCC4"},{id:"profile",label:"My Account",href:"/profile",icon:"\uD83D\uDC64",children:[{id:"profile-info",label:"Profile Info",href:"/profile",icon:"\uD83D\uDC64"},{id:"company",label:"Company Details",href:"/profile/company",icon:"\uD83C\uDFE2"},{id:"preferences",label:"Preferences",href:"/profile/preferences",icon:"⚙️"},{id:"security",label:"Security",href:"/profile/security",icon:"\uD83D\uDD12"}]}];function E(e){let{isCollapsed:s=!1,onToggleCollapse:r}=e,c=(0,o.usePathname)(),{isRTL:d}=(0,l.o)(),[h,x]=(0,a.useState)(["products","orders"]),u=e=>{x(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},p=e=>"/"===e?"/"===c:c.startsWith(e),f=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=e.children&&e.children.length>0,l=h.includes(e.id),c=p(e.href);return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)(i(),{href:e.href,className:(0,n.cn)("flex items-center flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors",r>0&&"ml-6",c?"bg-primary-100 text-primary-700":"text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50"),children:[(0,t.jsx)("span",{className:"flex-shrink-0",children:e.icon}),!s&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:(0,n.cn)("ml-3",d&&"mr-3 ml-0"),children:e.label}),e.badge&&(0,t.jsx)(m.Ex,{variant:"primary",size:"sm",className:"ml-auto",children:e.badge})]})]}),a&&!s&&(0,t.jsx)(m.$n,{variant:"ghost",size:"sm",onClick:()=>u(e.id),className:"p-1 ml-1",children:(0,t.jsx)("svg",{className:(0,n.cn)("h-4 w-4 transition-transform",l&&"rotate-90"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),a&&!s&&l&&e.children&&(0,t.jsx)("div",{className:"mt-1 space-y-1",children:e.children.map(e=>f(e,r+1))})]},e.id)};return(0,t.jsx)("div",{className:(0,n.cn)("bg-white border-r border-secondary-200 transition-all duration-300",s?"w-16":"w-64"),children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[!s&&(0,t.jsx)("h2",{className:"text-lg font-semibold text-secondary-900",children:"Navigation"}),(0,t.jsx)(m.$n,{variant:"ghost",size:"sm",onClick:r,className:"p-1",children:(0,t.jsx)("svg",{className:(0,n.cn)("h-4 w-4 transition-transform",s&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})})})]}),(0,t.jsx)("nav",{className:"space-y-2",children:S.map(e=>f(e))}),!s&&(0,t.jsxs)("div",{className:"mt-8 pt-6 border-t border-secondary-200",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-secondary-500 mb-3",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(m.$n,{variant:"outline",size:"sm",fullWidth:!0,className:"justify-start",children:[(0,t.jsx)("span",{className:"mr-2",children:"➕"}),"New Order"]}),(0,t.jsxs)(m.$n,{variant:"outline",size:"sm",fullWidth:!0,className:"justify-start",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83D\uDCDE"}),"Contact Support"]})]})]})]})})}function P(e){let{children:s}=e,[r,c]=(0,a.useState)(!1),{isRTL:i}=(0,l.o)();return(0,t.jsxs)("div",{className:(0,n.cn)("min-h-screen bg-gray-50",i&&"rtl"),children:[(0,t.jsx)(L,{}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("aside",{className:"hidden lg:block",children:(0,t.jsx)(E,{isCollapsed:r,onToggleCollapse:()=>c(!r)})}),(0,t.jsx)("main",{className:"flex-1 min-h-screen",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)(x,{}),(0,t.jsx)("div",{className:"max-w-full",children:s})]})})]}),(0,t.jsx)("footer",{className:"bg-white border-t border-secondary-200 mt-auto",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Company"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Careers"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Press"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Blog"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Support"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Help Center"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Contact Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Returns"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Shipping Info"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Legal"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Privacy Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Terms of Service"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Cookie Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"GDPR"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Connect"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Twitter"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"LinkedIn"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Facebook"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Instagram"})})]})]})]}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-secondary-200",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-primary-600 rounded flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"E"})}),(0,t.jsx)("span",{className:"text-sm text-secondary-600",children:"\xa9 2024 ElectroShop. All rights reserved."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,t.jsx)("span",{className:"text-sm text-secondary-600",children:"Powered by Next.js"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDCB3"}),(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDD12"}),(0,t.jsx)("span",{className:"text-lg",children:"✅"})]})]})]})})]})})]})}},1427:(e,s,r)=>{Promise.resolve().then(r.bind(r,394))},7740:(e,s,r)=>{"use strict";r.d(s,{D:()=>c,N:()=>n});var t=r(5155),a=r(2115);let l=(0,a.createContext)(void 0);function n(e){let{children:s}=e,[r,n]=(0,a.useState)("system"),[c,i]=(0,a.useState)("light");(0,a.useEffect)(()=>{let e=localStorage.getItem("theme");e&&["light","dark","system"].includes(e)&&n(e)},[]),(0,a.useEffect)(()=>{let e=()=>{"system"===r?i(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"):i(r)};e();let s=window.matchMedia("(prefers-color-scheme: dark)"),t=()=>{"system"===r&&e()};return s.addEventListener("change",t),()=>s.removeEventListener("change",t)},[r]),(0,a.useEffect)(()=>{let e=document.documentElement;"dark"===c?e.classList.add("dark"):e.classList.remove("dark")},[c]);let o=e=>{n(e),localStorage.setItem("theme",e)};return(0,t.jsx)(l.Provider,{value:{theme:r,actualTheme:c,setTheme:o,toggleTheme:()=>{"light"===r?o("dark"):"dark"===r?o("system"):o("light")}},children:s})}function c(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},9283:(e,s,r)=>{"use strict";r.d(s,{I:()=>c,o:()=>i});var t=r(5155),a=r(2115);let l=(0,a.createContext)(void 0),n=["ar"];function c(e){let{children:s}=e,[r,c]=(0,a.useState)("en");(0,a.useEffect)(()=>{let e=localStorage.getItem("locale");if(e&&["en","ar"].includes(e))c(e);else{let e=navigator.language.split("-")[0];["en","ar"].includes(e)&&c(e)}},[]);let i=n.includes(r),o=i?"rtl":"ltr";(0,a.useEffect)(()=>{document.documentElement.dir=o,document.documentElement.lang=r},[r,o]);let d=e=>{c(e),localStorage.setItem("locale",e)};return(0,t.jsx)(l.Provider,{value:{locale:r,setLocale:d,isRTL:i,direction:o,toggleLanguage:()=>{d("en"===r?"ar":"en")}},children:s})}function i(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}}},e=>{var s=s=>e(e.s=s);e.O(0,[635,3,874,680,750,441,684,358],()=>s(1427)),_N_E=e.O()}]);