(()=>{var e={};e.id=571,e.ids=[571],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4923:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>ez});var o=t(687),s=t(3210),a=t.n(s);function n(){for(var e,r,t=0,o="",s=arguments.length;t<s;t++)(e=arguments[t])&&(r=function e(r){var t,o,s="";if("string"==typeof r||"number"==typeof r)s+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(s&&(s+=" "),s+=o)}else for(o in r)r[o]&&(s&&(s+=" "),s+=o);return s}(e))&&(o&&(o+=" "),o+=r);return o}let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:a}=r,i=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],o=null==a?void 0:a[e];if(null===r)return null;let n=l(r)||l(o);return s[e][n]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return n(e,i,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...a,...d}[r]):({...a,...d})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},d=e=>{let r=m(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),c(t,r)||p(e)},getConflictingClassGroupIds:(e,r)=>{let s=t[e]||[];return r&&o[e]?[...s,...o[e]]:s}}},c=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),s=o?c(e.slice(1),o):void 0;if(s)return s;if(0===r.validators.length)return;let a=e.join("-");return r.validators.find(({validator:e})=>e(a))?.classGroupId},u=/^\[(.+)\]$/,p=e=>{if(u.test(e)){let r=u.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},m=e=>{let{theme:r,prefix:t}=e,o={nextPart:new Map,validators:[]};return g(Object.entries(e.classGroups),t).forEach(([e,t])=>{f(t,o,e,r)}),o},f=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:b(r,e)).classGroupId=t;return}if("function"==typeof e)return x(e)?void f(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,s])=>{f(s,b(r,e),t,o)})})},b=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},x=e=>e.isThemeGetter,g=(e,r)=>r?e.map(([e,t])=>[e,t.map(e=>"string"==typeof e?r+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,t])=>[r+e,t])):e)]):e,h=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,s=(s,a)=>{t.set(s,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(s(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):s(e,r)}}},v=e=>{let{separator:r,experimentalParseClassName:t}=e,o=1===r.length,s=r[0],a=r.length,n=e=>{let t,n=[],l=0,i=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===l){if(c===s&&(o||e.slice(d,d+a)===r)){n.push(e.slice(i,d)),i=d+a;continue}if("/"===c){t=d;continue}}"["===c?l++:"]"===c&&l--}let d=0===n.length?e:e.substring(i),c=d.startsWith("!"),u=c?d.substring(1):d;return{modifiers:n,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};return t?e=>t({className:e,parseClassName:n}):n},y=e=>{if(e.length<=1)return e;let r=[],t=[];return e.forEach(e=>{"["===e[0]?(r.push(...t.sort(),e),t=[]):t.push(e)}),r.push(...t.sort()),r},w=e=>({cache:h(e.cacheSize),parseClassName:v(e),...d(e)}),j=/\s+/,N=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:s}=r,a=[],n=e.trim().split(j),l="";for(let e=n.length-1;e>=0;e-=1){let r=n[e],{modifiers:i,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:u}=t(r),p=!!u,m=o(p?c.substring(0,u):c);if(!m){if(!p||!(m=o(c))){l=r+(l.length>0?" "+l:l);continue}p=!1}let f=y(i).join(":"),b=d?f+"!":f,x=b+m;if(a.includes(x))continue;a.push(x);let g=s(m,p);for(let e=0;e<g.length;++e){let r=g[e];a.push(b+r)}l=r+(l.length>0?" "+l:l)}return l};function k(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=z(e))&&(o&&(o+=" "),o+=r);return o}let z=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=z(e[o]))&&(t&&(t+=" "),t+=r);return t},C=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},R=/^\[(?:([a-z-]+):)?(.+)\]$/i,P=/^\d+\/\d+$/,M=new Set(["px","full","screen"]),_=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,L=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,D=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,G=e=>q(e)||M.has(e)||P.test(e),A=e=>J(e,"length",Q),q=e=>!!e&&!Number.isNaN(Number(e)),O=e=>J(e,"number",q),T=e=>!!e&&Number.isInteger(Number(e)),$=e=>e.endsWith("%")&&q(e.slice(0,-1)),B=e=>R.test(e),I=e=>_.test(e),V=new Set(["length","size","percentage"]),W=e=>J(e,V,Y),U=e=>J(e,"position",Y),F=new Set(["image","url"]),H=e=>J(e,F,ee),K=e=>J(e,"",Z),X=()=>!0,J=(e,r,t)=>{let o=R.exec(e);return!!o&&(o[1]?"string"==typeof r?o[1]===r:r.has(o[1]):t(o[2]))},Q=e=>L.test(e)&&!S.test(e),Y=()=>!1,Z=e=>D.test(e),ee=e=>E.test(e);Symbol.toStringTag;let er=function(e,...r){let t,o,s,a=function(l){return o=(t=w(r.reduce((e,r)=>r(e),e()))).cache.get,s=t.cache.set,a=n,n(l)};function n(e){let r=o(e);if(r)return r;let a=N(e,t);return s(e,a),a}return function(){return a(k.apply(null,arguments))}}(()=>{let e=C("colors"),r=C("spacing"),t=C("blur"),o=C("brightness"),s=C("borderColor"),a=C("borderRadius"),n=C("borderSpacing"),l=C("borderWidth"),i=C("contrast"),d=C("grayscale"),c=C("hueRotate"),u=C("invert"),p=C("gap"),m=C("gradientColorStops"),f=C("gradientColorStopPositions"),b=C("inset"),x=C("margin"),g=C("opacity"),h=C("padding"),v=C("saturate"),y=C("scale"),w=C("sepia"),j=C("skew"),N=C("space"),k=C("translate"),z=()=>["auto","contain","none"],R=()=>["auto","hidden","clip","visible","scroll"],P=()=>["auto",B,r],M=()=>[B,r],_=()=>["",G,A],L=()=>["auto",q,B],S=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],D=()=>["solid","dashed","dotted","double","none"],E=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],V=()=>["start","end","center","between","around","evenly","stretch"],F=()=>["","0",B],J=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Q=()=>[q,B];return{cacheSize:500,separator:":",theme:{colors:[X],spacing:[G,A],blur:["none","",I,B],brightness:Q(),borderColor:[e],borderRadius:["none","","full",I,B],borderSpacing:M(),borderWidth:_(),contrast:Q(),grayscale:F(),hueRotate:Q(),invert:F(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[$,A],inset:P(),margin:P(),opacity:Q(),padding:M(),saturate:Q(),scale:Q(),sepia:F(),skew:Q(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",B]}],container:["container"],columns:[{columns:[I]}],"break-after":[{"break-after":J()}],"break-before":[{"break-before":J()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...S(),B]}],overflow:[{overflow:R()}],"overflow-x":[{"overflow-x":R()}],"overflow-y":[{"overflow-y":R()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[b]}],"inset-x":[{"inset-x":[b]}],"inset-y":[{"inset-y":[b]}],start:[{start:[b]}],end:[{end:[b]}],top:[{top:[b]}],right:[{right:[b]}],bottom:[{bottom:[b]}],left:[{left:[b]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",T,B]}],basis:[{basis:P()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",B]}],grow:[{grow:F()}],shrink:[{shrink:F()}],order:[{order:["first","last","none",T,B]}],"grid-cols":[{"grid-cols":[X]}],"col-start-end":[{col:["auto",{span:["full",T,B]},B]}],"col-start":[{"col-start":L()}],"col-end":[{"col-end":L()}],"grid-rows":[{"grid-rows":[X]}],"row-start-end":[{row:["auto",{span:[T,B]},B]}],"row-start":[{"row-start":L()}],"row-end":[{"row-end":L()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",B]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",B]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...V()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...V(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...V(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",B,r]}],"min-w":[{"min-w":[B,r,"min","max","fit"]}],"max-w":[{"max-w":[B,r,"none","full","min","max","fit","prose",{screen:[I]},I]}],h:[{h:[B,r,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[B,r,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[B,r,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[B,r,"auto","min","max","fit"]}],"font-size":[{text:["base",I,A]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",O]}],"font-family":[{font:[X]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",B]}],"line-clamp":[{"line-clamp":["none",q,O]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",G,B]}],"list-image":[{"list-image":["none",B]}],"list-style-type":[{list:["none","disc","decimal",B]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...D(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",G,A]}],"underline-offset":[{"underline-offset":["auto",G,B]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",B]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",B]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...S(),U]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",W]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},H]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...D(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:D()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...D()]}],"outline-offset":[{"outline-offset":[G,B]}],"outline-w":[{outline:[G,A]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:_()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[G,A]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",I,K]}],"shadow-color":[{shadow:[X]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...E(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":E()}],filter:[{filter:["","none"]}],blur:[{blur:[t]}],brightness:[{brightness:[o]}],contrast:[{contrast:[i]}],"drop-shadow":[{"drop-shadow":["","none",I,B]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[t]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[i]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[n]}],"border-spacing-x":[{"border-spacing-x":[n]}],"border-spacing-y":[{"border-spacing-y":[n]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",B]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",B]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",B]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[T,B]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",B]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",B]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",B]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[G,A,O]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function et(...e){return er(n(e))}let eo=i("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{variants:{variant:{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-secondary-300 bg-white text-secondary-700 hover:bg-secondary-50 focus:ring-secondary-500",ghost:"text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500",danger:"bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm",success:"bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm"},size:{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base",xl:"h-14 px-8 text-lg"},fullWidth:{true:"w-full",false:"w-auto"}},defaultVariants:{variant:"primary",size:"md",fullWidth:!1}}),es=a().forwardRef(({className:e,variant:r,size:t,fullWidth:s,loading:a,leftIcon:n,rightIcon:l,children:i,disabled:d,...c},u)=>(0,o.jsxs)("button",{className:et(eo({variant:r,size:t,fullWidth:s,className:e})),ref:u,disabled:d||a,...c,children:[a&&(0,o.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,o.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,o.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!a&&n&&(0,o.jsx)("span",{className:"mr-2",children:n}),i,!a&&l&&(0,o.jsx)("span",{className:"ml-2",children:l})]}));es.displayName="Button";let ea=i("flex w-full rounded-lg border bg-white px-3 py-2 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-secondary-300 focus:border-primary-500 focus:ring-primary-500",error:"border-error-500 focus:border-error-500 focus:ring-error-500",success:"border-success-500 focus:border-success-500 focus:ring-success-500"},size:{sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"}},defaultVariants:{variant:"default",size:"md"}}),en=a().forwardRef(({className:e,variant:r,size:t,type:s="text",label:a,helperText:n,error:l,leftIcon:i,rightIcon:d,containerClassName:c,id:u,...p},m)=>{let f=u||`input-${Math.random().toString(36).substr(2,9)}`,b=!!l;return(0,o.jsxs)("div",{className:et("space-y-1",c),children:[a&&(0,o.jsxs)("label",{htmlFor:f,className:"block text-sm font-medium text-secondary-700",children:[a,p.required&&(0,o.jsx)("span",{className:"text-error-500 ml-1",children:"*"})]}),(0,o.jsxs)("div",{className:"relative",children:[i&&(0,o.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:i}),(0,o.jsx)("input",{type:s,className:et(ea({variant:b?"error":r,size:t}),i&&"pl-10",d&&"pr-10",e),ref:m,id:f,...p}),d&&(0,o.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:d})]}),(l||n)&&(0,o.jsx)("p",{className:et("text-xs",l?"text-error-600":"text-secondary-500"),children:l||n})]})});en.displayName="Input";let el=i("rounded-lg bg-white transition-all duration-200",{variants:{variant:{default:"border border-secondary-200 shadow-sm",outlined:"border border-secondary-300",elevated:"shadow-lg border border-secondary-100",ghost:"border-0 shadow-none"},padding:{none:"p-0",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"},hover:{true:"hover:shadow-md hover:border-secondary-300",false:""}},defaultVariants:{variant:"default",padding:"md",hover:!1}}),ei=a().forwardRef(({className:e,variant:r,padding:t,hover:s,children:a,...n},l)=>(0,o.jsx)("div",{ref:l,className:et(el({variant:r,padding:t,hover:s}),e),...n,children:a}));ei.displayName="Card";let ed=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("flex flex-col space-y-1.5 pb-4",e),...r}));ed.displayName="CardHeader";let ec=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:et("text-lg font-semibold leading-none tracking-tight text-secondary-900",e),...r}));ec.displayName="CardTitle";let eu=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:et("text-sm text-secondary-600",e),...r}));eu.displayName="CardDescription";let ep=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("",e),...r}));ep.displayName="CardContent";let em=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("flex items-center pt-4",e),...r}));em.displayName="CardFooter";let ef=i("inline-flex items-center rounded-full font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",primary:"bg-primary-100 text-primary-800 hover:bg-primary-200",secondary:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",success:"bg-success-100 text-success-800 hover:bg-success-200",warning:"bg-warning-100 text-warning-800 hover:bg-warning-200",error:"bg-error-100 text-error-800 hover:bg-error-200",outline:"border border-secondary-300 text-secondary-700 hover:bg-secondary-50"},size:{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},dot:{true:"pl-1.5",false:""}},defaultVariants:{variant:"default",size:"md",dot:!1}}),eb=a().forwardRef(({className:e,variant:r,size:t,dot:s,children:a,dotColor:n,...l},i)=>(0,o.jsxs)("div",{ref:i,className:et(ef({variant:r,size:t,dot:s}),e),...l,children:[s&&(0,o.jsx)("span",{className:et("mr-1.5 h-1.5 w-1.5 rounded-full",n||"bg-current")}),a]}));eb.displayName="Badge";let ex=i("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-white border-secondary-200 text-secondary-900",info:"bg-primary-50 border-primary-200 text-primary-900 [&>svg]:text-primary-600",success:"bg-success-50 border-success-200 text-success-900 [&>svg]:text-success-600",warning:"bg-warning-50 border-warning-200 text-warning-900 [&>svg]:text-warning-600",error:"bg-error-50 border-error-200 text-error-900 [&>svg]:text-error-600"}},defaultVariants:{variant:"default"}}),eg=a().forwardRef(({className:e,variant:r,children:t,dismissible:s,onDismiss:n,icon:l,...i},d)=>{let[c,u]=a().useState(!0);return c?(0,o.jsxs)("div",{ref:d,role:"alert",className:et(ex({variant:r}),e),...i,children:[l,(0,o.jsx)("div",{className:"flex-1",children:t}),s&&(0,o.jsx)("button",{onClick:()=>{u(!1),n?.()},className:"absolute right-2 top-2 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2","aria-label":"Close alert",children:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):null});eg.displayName="Alert";let eh=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("h5",{ref:t,className:et("mb-1 font-medium leading-none tracking-tight",e),...r}));eh.displayName="AlertTitle";let ev=a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("text-sm [&_p]:leading-relaxed",e),...r}));ev.displayName="AlertDescription";let ey={info:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),success:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),warning:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),error:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,o.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})},ew=i("relative bg-white rounded-lg shadow-xl transform transition-all",{variants:{size:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl",full:"max-w-full mx-4"}},defaultVariants:{size:"md"}});a().forwardRef(({className:e,size:r,isOpen:t,onClose:s,children:n,closeOnBackdropClick:l=!0,closeOnEscape:i=!0,showCloseButton:d=!0,...c},u)=>(a().useEffect(()=>{if(!i)return;let e=e=>{"Escape"===e.key&&t&&s()};return t&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[t,s,i]),t)?(0,o.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,o.jsxs)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",onClick:e=>{l&&e.target===e.currentTarget&&s()},children:[(0,o.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity"}),(0,o.jsxs)("div",{ref:u,className:et(ew({size:r}),"relative w-full",e),...c,children:[d&&(0,o.jsx)("button",{onClick:s,className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-10","aria-label":"Close modal",children:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),n]})]})}):null).displayName="Modal",a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-4",e),...r})).displayName="ModalHeader",a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("h3",{ref:t,className:et("text-lg font-semibold leading-none tracking-tight text-secondary-900",e),...r})).displayName="ModalTitle",a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("p",{ref:t,className:et("text-sm text-secondary-600",e),...r})).displayName="ModalDescription",a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("px-6 py-4",e),...r})).displayName="ModalContent",a().forwardRef(({className:e,...r},t)=>(0,o.jsx)("div",{ref:t,className:et("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-4",e),...r})).displayName="ModalFooter";var ej=t(522),eN=t(2972),ek=t(3710);function ez(){let e=(0,ej.jL)(),{filteredItems:r,loading:t,searchQuery:a}=(0,ej.GV)(e=>e.products),[n,l]=(0,s.useState)(!1),i=r=>{e((0,ek.bE)({product:r})),l(!0),setTimeout(()=>l(!1),3e3)},d=r=>{e((0,eN.Ri)(r))};return(0,o.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,o.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Product Catalog"}),(0,o.jsx)("p",{className:"text-gray-600 mb-6",children:"Discover our latest collection of premium electronics and technology products."}),n&&(0,o.jsxs)(eg,{variant:"success",dismissible:!0,onDismiss:()=>l(!1),icon:ey.success,className:"mb-6",children:[(0,o.jsx)(eh,{children:"Product Added!"}),(0,o.jsx)(ev,{children:"The product has been successfully added to your cart."})]}),(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,o.jsx)(en,{placeholder:"Search products...",value:a,onChange:e=>d(e.target.value),className:"sm:max-w-sm",leftIcon:(0,o.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,o.jsx)(es,{variant:"outline",children:"Filter"}),(0,o.jsx)(es,{variant:"outline",children:"Sort"})]})]}),t?(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Loading products..."})]}):(0,o.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,o.jsxs)(ei,{hover:!0,className:"h-full flex flex-col",children:[(0,o.jsxs)(ed,{children:[(0,o.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center",children:(0,o.jsx)("span",{className:"text-6xl",children:"\uD83D\uDCF1"})}),(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsx)(ec,{className:"text-lg",children:e.name}),(0,o.jsx)(eb,{variant:"primary",children:e.category.name})]}),(0,o.jsx)(eu,{className:"line-clamp-2",children:e.description})]}),(0,o.jsx)(ep,{className:"flex-1",children:(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-500",children:"SKU:"}),(0,o.jsx)("span",{className:"text-sm font-mono",children:e.sku})]}),(0,o.jsxs)("div",{className:"flex justify-between items-center",children:[(0,o.jsx)("span",{className:"text-sm text-gray-500",children:"Stock:"}),(0,o.jsx)(eb,{variant:e.stock>10?"success":e.stock>0?"warning":"error",children:e.stock>0?`${e.stock} available`:"Out of stock"})]})]})}),(0,o.jsxs)(em,{className:"flex justify-between items-center",children:[(0,o.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:["$",e.price.toLocaleString()]}),(0,o.jsx)(es,{onClick:()=>i(e),disabled:0===e.stock,size:"sm",children:0===e.stock?"Out of Stock":"Add to Cart"})]})]},e.id))}),!t&&0===r.length&&(0,o.jsxs)("div",{className:"text-center py-12",children:[(0,o.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,o.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products found"}),(0,o.jsx)("p",{className:"text-gray-600",children:"Try adjusting your search or filter criteria."})]})]})})}},7002:(e,r,t)=>{Promise.resolve().then(t.bind(t,4923))},7798:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var o=t(5239),s=t(8088),a=t(8170),n=t.n(a),l=t(893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(r,i);let d={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8547)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/products/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/products/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8547:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/products/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/products/page.tsx","default")},8850:(e,r,t)=>{Promise.resolve().then(t.bind(t,8547))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[150,933],()=>t(7798));module.exports=o})();