(()=>{var e={};e.id=246,e.ids=[246],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4556:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(7413);function a(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Contracts"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsx)("p",{className:"text-gray-600",children:"Contract management page - Coming soon!"})})]})})}},6487:()=>{},8335:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9854:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>c});var s=t(5239),a=t(8088),n=t(8170),o=t.n(n),i=t(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let c={children:["",{children:["contracts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4556)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/contracts/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/contracts/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contracts/page",pathname:"/contracts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[150,933],()=>t(9854));module.exports=s})();