(()=>{var e={};e.id=610,e.ids=[610],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},6487:()=>{},8335:()=>{},8666:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(7413);function n(){return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Contracts"}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow p-6",children:(0,s.jsx)("p",{className:"text-gray-600",children:"Contract management page - Coming soon!"})})]})})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9176:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>u,tree:()=>c});var s=t(5239),n=t(8088),o=t(8170),a=t.n(o),i=t(893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let c={children:["",{children:["(portal)",{children:["contracts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8666)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/contracts/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,9785)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4431)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/contracts/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(portal)/contracts/page",pathname:"/contracts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[150,814,73,933,97],()=>t(9176));module.exports=s})();