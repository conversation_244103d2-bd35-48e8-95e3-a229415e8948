(()=>{var e={};e.id=589,e.ids=[589],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2826:(e,s,r)=>{Promise.resolve().then(r.bind(r,7211))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4546:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o});var n=r(5239),a=r(8088),t=r(8170),i=r.n(t),l=r(893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(s,d);let o={children:["",{children:["(portal)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6517)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9785)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(portal)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},6517:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/dashboard/page.tsx","default")},7211:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var n=r(687),a=r(3210),t=r(665);function i(){let[e,s]=(0,a.useState)(!1),[r,i]=(0,a.useState)(!1),[l,d]=(0,a.useState)(""),o=[{value:"option1",label:"Option 1"},{value:"option2",label:"Option 2"},{value:"option3",label:"Option 3"}];return(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"UI Components Dashboard"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Showcase of all available UI components in the ElectroShop design system."})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Alerts"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)(t.Fc,{variant:"info",icon:t.bf.info,children:[(0,n.jsx)(t.XL,{children:"Information"}),(0,n.jsx)(t.TN,{children:"This is an informational alert message."})]}),(0,n.jsxs)(t.Fc,{variant:"success",icon:t.bf.success,children:[(0,n.jsx)(t.XL,{children:"Success"}),(0,n.jsx)(t.TN,{children:"Your action was completed successfully!"})]}),(0,n.jsxs)(t.Fc,{variant:"warning",icon:t.bf.warning,children:[(0,n.jsx)(t.XL,{children:"Warning"}),(0,n.jsx)(t.TN,{children:"Please review your settings before proceeding."})]}),(0,n.jsxs)(t.Fc,{variant:"error",icon:t.bf.error,dismissible:!0,children:[(0,n.jsx)(t.XL,{children:"Error"}),(0,n.jsx)(t.TN,{children:"Something went wrong. Please try again."})]})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Buttons"}),(0,n.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,n.jsx)(t.$n,{variant:"primary",children:"Primary"}),(0,n.jsx)(t.$n,{variant:"secondary",children:"Secondary"}),(0,n.jsx)(t.$n,{variant:"outline",children:"Outline"}),(0,n.jsx)(t.$n,{variant:"ghost",children:"Ghost"}),(0,n.jsx)(t.$n,{variant:"danger",children:"Danger"}),(0,n.jsx)(t.$n,{variant:"success",children:"Success"}),(0,n.jsx)(t.$n,{loading:!0,children:"Loading"}),(0,n.jsx)(t.$n,{disabled:!0,children:"Disabled"}),(0,n.jsx)(t.$n,{size:"sm",children:"Small"}),(0,n.jsx)(t.$n,{size:"lg",children:"Large"}),(0,n.jsx)(t.$n,{leftIcon:"\uD83D\uDE80",children:"With Icon"})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Form Components"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,n.jsxs)(t.Zp,{children:[(0,n.jsx)(t.aR,{children:(0,n.jsx)(t.ZB,{children:"Input Components"})}),(0,n.jsxs)(t.Wu,{className:"space-y-4",children:[(0,n.jsx)(t.pd,{label:"Email",type:"email",placeholder:"Enter your email"}),(0,n.jsx)(t.pd,{label:"Password",type:"password",placeholder:"Enter password",helperText:"Must be at least 8 characters"}),(0,n.jsx)(t.pd,{label:"Search",placeholder:"Search products...",leftIcon:"\uD83D\uDD0D"}),(0,n.jsx)(t.pd,{label:"Amount",type:"number",placeholder:"0.00",rightIcon:"\uD83D\uDCB0"}),(0,n.jsx)(t.pd,{label:"Error Example",error:"This field is required",placeholder:"Required field"})]})]}),(0,n.jsxs)(t.Zp,{children:[(0,n.jsx)(t.aR,{children:(0,n.jsx)(t.ZB,{children:"Select & Toggle"})}),(0,n.jsxs)(t.Wu,{className:"space-y-4",children:[(0,n.jsx)(t.l6,{label:"Choose Option",options:o,value:l,onChange:d,placeholder:"Select an option..."}),(0,n.jsx)(t.l6,{label:"Searchable Select",options:o,searchable:!0,placeholder:"Search options..."}),(0,n.jsx)(t.lM,{checked:r,onChange:i,label:"Enable notifications",description:"Receive email notifications for updates"}),(0,n.jsx)(t.lM,{checked:!0,label:"Premium features",variant:"success"})]})]})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Cards & Badges"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,n.jsxs)(t.Zp,{hover:!0,children:[(0,n.jsxs)(t.aR,{children:[(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsx)(t.ZB,{children:"Product Card"}),(0,n.jsx)(t.Ex,{variant:"success",children:"In Stock"})]}),(0,n.jsx)(t.BT,{children:"This is a sample product card with hover effects."})]}),(0,n.jsx)(t.Wu,{children:(0,n.jsx)("div",{className:"text-2xl font-bold text-primary-600",children:"$299.99"})}),(0,n.jsx)(t.wL,{children:(0,n.jsx)(t.$n,{fullWidth:!0,children:"Add to Cart"})})]}),(0,n.jsxs)(t.Zp,{variant:"outlined",children:[(0,n.jsxs)(t.aR,{children:[(0,n.jsx)(t.ZB,{children:"Outlined Card"}),(0,n.jsx)(t.BT,{children:"Card with outlined variant styling."})]}),(0,n.jsx)(t.Wu,{children:(0,n.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,n.jsx)(t.Ex,{variant:"primary",children:"Primary"}),(0,n.jsx)(t.Ex,{variant:"secondary",children:"Secondary"}),(0,n.jsx)(t.Ex,{variant:"success",children:"Success"}),(0,n.jsx)(t.Ex,{variant:"warning",children:"Warning"}),(0,n.jsx)(t.Ex,{variant:"error",children:"Error"})]})})]}),(0,n.jsxs)(t.Zp,{variant:"elevated",children:[(0,n.jsxs)(t.aR,{children:[(0,n.jsx)(t.ZB,{children:"Elevated Card"}),(0,n.jsx)(t.BT,{children:"Card with elevated shadow styling."})]}),(0,n.jsx)(t.Wu,{children:(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(t.Ex,{size:"sm",dot:!0,children:"Small Badge"}),(0,n.jsx)(t.Ex,{size:"md",dot:!0,children:"Medium Badge"}),(0,n.jsx)(t.Ex,{size:"lg",dot:!0,children:"Large Badge"})]})})]})]})]}),(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Interactive Components"}),(0,n.jsxs)("div",{className:"flex gap-4",children:[(0,n.jsx)(t.$n,{onClick:()=>s(!0),children:"Open Modal"}),(0,n.jsx)(t.ms,{trigger:(0,n.jsx)(t.$n,{variant:"outline",children:"Dropdown Menu"}),items:[{key:"edit",label:"Edit",icon:"✏️"},{key:"duplicate",label:"Duplicate",icon:"\uD83D\uDCCB"},{key:"delete",label:"Delete",icon:"\uD83D\uDDD1️",danger:!0}]})]})]}),(0,n.jsxs)(t.aF,{isOpen:e,onClose:()=>s(!1),children:[(0,n.jsx)(t.rQ,{children:(0,n.jsx)(t.wt,{children:"Sample Modal"})}),(0,n.jsx)(t.$m,{children:(0,n.jsx)("p",{className:"text-gray-600",children:"This is a sample modal dialog. You can put any content here including forms, images, or other components."})}),(0,n.jsxs)(t.jl,{children:[(0,n.jsx)(t.$n,{variant:"outline",onClick:()=>s(!1),children:"Cancel"}),(0,n.jsx)(t.$n,{onClick:()=>s(!1),children:"Confirm"})]})]})]})})}},8322:(e,s,r)=>{Promise.resolve().then(r.bind(r,6517))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var s=require("../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),n=s.X(0,[150,814,73,933,97],()=>r(4546));module.exports=n})();