(()=>{var e={};e.id=815,e.ids=[815],e.modules={529:(e,t,r)=>{var s={"./ar/common.json":[2941,941],"./ar/orders.json":[8005,5],"./ar/products.json":[8444,444],"./en/common.json":[5405,405],"./en/orders.json":[3989,989],"./en/products.json":[8172,172]};function n(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],n=t[0];return r.e(t[1]).then(()=>r.t(n,19))}n.keys=()=>Object.keys(s),n.id=529,e.exports=n},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1498:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(687),n=r(3210),o=r(665),a=r(4393);let i={};function c(e="common"){let{locale:t,isRTL:r}=(0,a.o)(),[s,o]=(0,n.useState)({}),[i,l]=(0,n.useState)(!0);return{t:(e,t)=>(function(e,t,r={}){let s=function(e,t){let r=t.split(".").reduce((e,t)=>e&&void 0!==e[t]?e[t]:void 0,e);return"string"==typeof r?r:void 0}(t,e);return"string"==typeof s?function(e,t={}){return e.replace(/\{(\w+)\}/g,(e,r)=>t[r]?.toString()||e)}(s,r):e})(e,s,t),locale:t,isRTL:r,formatCurrency:(e,r)=>new Intl.NumberFormat(t,{style:"currency",currency:r}).format(e),formatDate:(e,r)=>(function(e,t,r){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat(t,r||{year:"numeric",month:"long",day:"numeric"}).format(s)})(e,t,r),formatNumber:(e,r)=>new Intl.NumberFormat(t,r).format(e),pluralize:(e,r,s)=>(function(e,t,r,s="en"){return"ar"!==s?1===e?t:r||t+"s":0===e?r||t:1===e?t:(2===e,r||t)})(e,r,s,t),loading:i}}var l=r(3710),d=r(2972),u=r(522);function m(){let e=(0,u.jL)(),{filteredItems:t,loading:r,searchQuery:a}=(0,u.GV)(e=>e.products),[i,m]=(0,n.useState)(!1),{t:p}=c("products"),{t:x}=c("common"),h=t=>{e((0,l.bE)({product:t})),m(!0),setTimeout(()=>m(!1),3e3)},j=t=>{e((0,d.Ri)(t))};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 p-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:p("title")}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:p("subtitle")}),i&&(0,s.jsxs)(o.Fc,{variant:"success",dismissible:!0,onDismiss:()=>m(!1),icon:o.bf.success,className:"mb-6",children:[(0,s.jsx)(o.XL,{children:x("success")}),(0,s.jsx)(o.TN,{children:p("cart.addedToCart")})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,s.jsx)(o.pd,{placeholder:p("search.placeholder"),value:a,onChange:e=>j(e.target.value),className:"sm:max-w-sm",leftIcon:(0,s.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,s.jsx)(o.$n,{variant:"outline",children:p("filters.title")}),(0,s.jsx)(o.$n,{variant:"outline",children:p("sort.title")})]})]}),r?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:x("loading")})]}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,s.jsxs)(o.Zp,{hover:!0,className:"h-full flex flex-col",children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg mb-4 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-6xl",children:"\uD83D\uDCF1"})}),(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)(o.ZB,{className:"text-lg",children:e.name}),(0,s.jsx)(o.Ex,{variant:"primary",children:p(`categories.${e.category.id}`)||e.category.name})]}),(0,s.jsx)(o.BT,{className:"line-clamp-2",children:e.description})]}),(0,s.jsx)(o.Wu,{className:"flex-1",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[p("product.sku"),":"]}),(0,s.jsx)("span",{className:"text-sm font-mono",children:e.sku})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:[p("product.stock"),":"]}),(0,s.jsx)(o.Ex,{variant:e.stock>10?"success":e.stock>0?"warning":"error",children:e.stock>0?`${e.stock} ${p("product.inStock")}`:p("product.outOfStock")})]})]})}),(0,s.jsxs)(o.wL,{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-primary-600",children:["$",e.price.toLocaleString()]}),(0,s.jsx)(o.$n,{onClick:()=>h(e),disabled:0===e.stock,size:"sm",children:0===e.stock?p("product.outOfStock"):p("product.addToCart")})]})]},e.id))}),!r&&0===t.length&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDD0D"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:p("search.noResults")}),(0,s.jsx)("p",{className:"text-gray-600",children:p("search.noResults")})]})]})})}},1636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var s=r(5239),n=r(8088),o=r(8170),a=r.n(o),i=r(893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l={children:["",{children:["(portal)",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5269)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,9785)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(portal)/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5269:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/(portal)/products/page.tsx","default")},5574:(e,t,r)=>{Promise.resolve().then(r.bind(r,1498))},7532:(e,t,r)=>{var s={"./common.json":[5405,405],"./orders.json":[3989,989],"./products.json":[8172,172]};function n(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],n=t[0];return r.e(t[1]).then(()=>r.t(n,19))}n.keys=()=>Object.keys(s),n.id=7532,e.exports=n},8878:(e,t,r)=>{Promise.resolve().then(r.bind(r,5269))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[150,814,73,933,97],()=>r(1636));module.exports=s})();