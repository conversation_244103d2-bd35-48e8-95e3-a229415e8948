exports.id=97,exports.ids=[97],exports.modules={665:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>c,TN:()=>x,bf:()=>m,XL:()=>d,Ex:()=>u,$n:()=>p,Zp:()=>g,Wu:()=>N,BT:()=>j,wL:()=>w,aR:()=>y,ZB:()=>v,ms:()=>k,pd:()=>L,aF:()=>R,$m:()=>P,jl:()=>D,rQ:()=>M,wt:()=>S,l6:()=>$,lM:()=>F});var t=r(687),a=r(4224),n=r(3210),l=r.n(n),o=r(1191);let i=(0,a.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-white border-secondary-200 text-secondary-900",info:"bg-primary-50 border-primary-200 text-primary-900 [&>svg]:text-primary-600",success:"bg-success-50 border-success-200 text-success-900 [&>svg]:text-success-600",warning:"bg-warning-50 border-warning-200 text-warning-900 [&>svg]:text-warning-600",error:"bg-error-50 border-error-200 text-error-900 [&>svg]:text-error-600"}},defaultVariants:{variant:"default"}}),c=l().forwardRef(({className:e,variant:s,children:r,dismissible:a,onDismiss:n,icon:c,...d},x)=>{let[m,h]=l().useState(!0);return m?(0,t.jsxs)("div",{ref:x,role:"alert",className:(0,o.cn)(i({variant:s}),e),...d,children:[c,(0,t.jsx)("div",{className:"flex-1",children:r}),a&&(0,t.jsx)("button",{onClick:()=>{h(!1),n?.()},className:"absolute right-2 top-2 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2","aria-label":"Close alert",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}):null});c.displayName="Alert";let d=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("h5",{ref:r,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",e),...s}));d.displayName="AlertTitle";let x=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",e),...s}));x.displayName="AlertDescription";let m={info:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),success:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),warning:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),error:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,t.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})},h=(0,a.F)("inline-flex items-center rounded-full font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",primary:"bg-primary-100 text-primary-800 hover:bg-primary-200",secondary:"bg-secondary-100 text-secondary-800 hover:bg-secondary-200",success:"bg-success-100 text-success-800 hover:bg-success-200",warning:"bg-warning-100 text-warning-800 hover:bg-warning-200",error:"bg-error-100 text-error-800 hover:bg-error-200",outline:"border border-secondary-300 text-secondary-700 hover:bg-secondary-50"},size:{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"},dot:{true:"pl-1.5",false:""}},defaultVariants:{variant:"default",size:"md",dot:!1}}),u=l().forwardRef(({className:e,variant:s,size:r,dot:a,children:n,dotColor:l,...i},c)=>(0,t.jsxs)("div",{ref:c,className:(0,o.cn)(h({variant:s,size:r,dot:a}),e),...i,children:[a&&(0,t.jsx)("span",{className:(0,o.cn)("mr-1.5 h-1.5 w-1.5 rounded-full",l||"bg-current")}),n]}));u.displayName="Badge";let f=(0,a.F)("inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",{variants:{variant:{primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500 shadow-sm",secondary:"bg-secondary-100 text-secondary-900 hover:bg-secondary-200 focus:ring-secondary-500",outline:"border border-secondary-300 bg-white text-secondary-700 hover:bg-secondary-50 focus:ring-secondary-500",ghost:"text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500",danger:"bg-error-600 text-white hover:bg-error-700 focus:ring-error-500 shadow-sm",success:"bg-success-600 text-white hover:bg-success-700 focus:ring-success-500 shadow-sm"},size:{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm",lg:"h-12 px-6 text-base",xl:"h-14 px-8 text-lg"},fullWidth:{true:"w-full",false:"w-auto"}},defaultVariants:{variant:"primary",size:"md",fullWidth:!1}}),p=l().forwardRef(({className:e,variant:s,size:r,fullWidth:a,loading:n,leftIcon:l,rightIcon:i,children:c,disabled:d,...x},m)=>(0,t.jsxs)("button",{className:(0,o.cn)(f({variant:s,size:r,fullWidth:a,className:e})),ref:m,disabled:d||n,...x,children:[n&&(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),!n&&l&&(0,t.jsx)("span",{className:"mr-2",children:l}),c,!n&&i&&(0,t.jsx)("span",{className:"ml-2",children:i})]}));p.displayName="Button";let b=(0,a.F)("rounded-lg bg-white transition-all duration-200",{variants:{variant:{default:"border border-secondary-200 shadow-sm",outlined:"border border-secondary-300",elevated:"shadow-lg border border-secondary-100",ghost:"border-0 shadow-none"},padding:{none:"p-0",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"},hover:{true:"hover:shadow-md hover:border-secondary-300",false:""}},defaultVariants:{variant:"default",padding:"md",hover:!1}}),g=l().forwardRef(({className:e,variant:s,padding:r,hover:a,children:n,...l},i)=>(0,t.jsx)("div",{ref:i,className:(0,o.cn)(b({variant:s,padding:r,hover:a}),e),...l,children:n}));g.displayName="Card";let y=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 pb-4",e),...s}));y.displayName="CardHeader";let v=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight text-secondary-900",e),...s}));v.displayName="CardTitle";let j=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-secondary-600",e),...s}));j.displayName="CardDescription";let N=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("",e),...s}));N.displayName="CardContent";let w=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center pt-4",e),...s}));w.displayName="CardFooter";let k=l().forwardRef(({trigger:e,items:s,align:r="left",className:a,menuClassName:l},i)=>{let[c,d]=(0,n.useState)(!1),x=(0,n.useRef)(null);(0,n.useEffect)(()=>{let e=e=>{x.current&&!x.current.contains(e.target)&&d(!1)};return c&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[c]);let m=e=>{!e.disabled&&e.onClick&&e.onClick(),d(!1)};return(0,t.jsxs)("div",{className:(0,o.cn)("relative inline-block",a),ref:x,children:[(0,t.jsx)("div",{onClick:()=>d(!c),className:"cursor-pointer",children:e}),c&&(0,t.jsx)("div",{className:(0,o.cn)("absolute z-50 mt-1 min-w-48 bg-white border border-secondary-300 rounded-lg shadow-lg py-1","right"===r?"right-0":"left-0",l),children:s.map(e=>(0,t.jsxs)("button",{type:"button",className:(0,o.cn)("w-full px-3 py-2 text-left text-sm flex items-center space-x-2 hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none",e.disabled&&"opacity-50 cursor-not-allowed",e.danger&&"text-error-600 hover:bg-error-50 focus:bg-error-50"),onClick:()=>m(e),disabled:e.disabled,children:[e.icon&&(0,t.jsx)("span",{className:"flex-shrink-0",children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.key))})]})});k.displayName="Dropdown";let C=(0,a.F)("flex w-full rounded-lg border bg-white px-3 py-2 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-secondary-500 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-secondary-300 focus:border-primary-500 focus:ring-primary-500",error:"border-error-500 focus:border-error-500 focus:ring-error-500",success:"border-success-500 focus:border-success-500 focus:ring-success-500"},size:{sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"}},defaultVariants:{variant:"default",size:"md"}}),L=l().forwardRef(({className:e,variant:s,size:r,type:a="text",label:n,helperText:l,error:i,leftIcon:c,rightIcon:d,containerClassName:x,id:m,...h},u)=>{let f=m||`input-${Math.random().toString(36).substr(2,9)}`,p=!!i;return(0,t.jsxs)("div",{className:(0,o.cn)("space-y-1",x),children:[n&&(0,t.jsxs)("label",{htmlFor:f,className:"block text-sm font-medium text-secondary-700",children:[n,h.required&&(0,t.jsx)("span",{className:"text-error-500 ml-1",children:"*"})]}),(0,t.jsxs)("div",{className:"relative",children:[c&&(0,t.jsx)("div",{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:c}),(0,t.jsx)("input",{type:a,className:(0,o.cn)(C({variant:p?"error":s,size:r}),c&&"pl-10",d&&"pr-10",e),ref:u,id:f,...h}),d&&(0,t.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400",children:d})]}),(i||l)&&(0,t.jsx)("p",{className:(0,o.cn)("text-xs",i?"text-error-600":"text-secondary-500"),children:i||l})]})});L.displayName="Input";let z=(0,a.F)("relative bg-white rounded-lg shadow-xl transform transition-all",{variants:{size:{sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl",full:"max-w-full mx-4"}},defaultVariants:{size:"md"}}),R=l().forwardRef(({className:e,size:s,isOpen:r,onClose:a,children:n,closeOnBackdropClick:i=!0,closeOnEscape:c=!0,showCloseButton:d=!0,...x},m)=>(l().useEffect(()=>{if(!c)return;let e=e=>{"Escape"===e.key&&r&&a()};return r&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[r,a,c]),r)?(0,t.jsx)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex min-h-full items-center justify-center p-4 text-center",onClick:e=>{i&&e.target===e.currentTarget&&a()},children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity"}),(0,t.jsxs)("div",{ref:m,className:(0,o.cn)(z({size:s}),"relative w-full",e),...x,children:[d&&(0,t.jsx)("button",{onClick:a,className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 z-10","aria-label":"Close modal",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),n]})]})}):null);R.displayName="Modal";let M=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left p-6 pb-4",e),...s}));M.displayName="ModalHeader";let S=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight text-secondary-900",e),...s}));S.displayName="ModalTitle",l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,o.cn)("text-sm text-secondary-600",e),...s})).displayName="ModalDescription";let P=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("px-6 py-4",e),...s}));P.displayName="ModalContent";let D=l().forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 p-6 pt-4",e),...s}));D.displayName="ModalFooter";let B=(0,a.F)("flex w-full items-center justify-between rounded-lg border bg-white px-3 py-2 text-sm transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",{variants:{variant:{default:"border-secondary-300 focus:border-primary-500 focus:ring-primary-500",error:"border-error-500 focus:border-error-500 focus:ring-error-500",success:"border-success-500 focus:border-success-500 focus:ring-success-500"},size:{sm:"h-8 text-xs",md:"h-10 text-sm",lg:"h-12 text-base"}},defaultVariants:{variant:"default",size:"md"}}),$=l().forwardRef(({className:e,variant:s,size:r,options:a,value:l,onChange:i,placeholder:c="Select an option...",label:d,helperText:x,error:m,searchable:h=!1,containerClassName:u,disabled:f,...p},b)=>{let[g,y]=(0,n.useState)(!1),[v,j]=(0,n.useState)(""),N=(0,n.useRef)(null),w=!!m,k=a.find(e=>e.value===l),C=h?a.filter(e=>e.label.toLowerCase().includes(v.toLowerCase())):a;(0,n.useEffect)(()=>{let e=e=>{N.current&&!N.current.contains(e.target)&&(y(!1),j(""))};return g&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[g]);let L=e=>{i?.(e),y(!1),j("")};return(0,t.jsxs)("div",{className:(0,o.cn)("relative",u),ref:N,children:[d&&(0,t.jsx)("label",{className:"block text-sm font-medium text-secondary-700 mb-1",children:d}),(0,t.jsxs)("button",{type:"button",ref:b,className:(0,o.cn)(B({variant:w?"error":s,size:r}),e),onClick:()=>!f&&y(!g),disabled:f,...p,children:[(0,t.jsx)("span",{className:(0,o.cn)("truncate",!k&&"text-secondary-500"),children:k?k.label:c}),(0,t.jsx)("svg",{className:(0,o.cn)("h-4 w-4 transition-transform",g&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),g&&(0,t.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-secondary-300 rounded-lg shadow-lg max-h-60 overflow-auto",children:[h&&(0,t.jsx)("div",{className:"p-2 border-b border-secondary-200",children:(0,t.jsx)("input",{type:"text",placeholder:"Search options...",value:v,onChange:e=>j(e.target.value),className:"w-full px-2 py-1 text-sm border border-secondary-300 rounded focus:outline-none focus:ring-1 focus:ring-primary-500"})}),(0,t.jsx)("div",{className:"py-1",children:0===C.length?(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-secondary-500",children:"No options found"}):C.map(e=>(0,t.jsx)("button",{type:"button",className:(0,o.cn)("w-full px-3 py-2 text-left text-sm hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none",e.value===l&&"bg-primary-50 text-primary-700",e.disabled&&"opacity-50 cursor-not-allowed"),onClick:()=>!e.disabled&&L(e.value),disabled:e.disabled,children:e.label},e.value))})]}),(m||x)&&(0,t.jsx)("p",{className:(0,o.cn)("mt-1 text-xs",m?"text-error-600":"text-secondary-500"),children:m||x})]})});$.displayName="Select";let E=(0,a.F)("peer inline-flex shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50",{variants:{size:{sm:"h-4 w-7",md:"h-5 w-9",lg:"h-6 w-11"},variant:{default:"data-[state=checked]:bg-primary-600 data-[state=unchecked]:bg-secondary-200",success:"data-[state=checked]:bg-success-600 data-[state=unchecked]:bg-secondary-200",warning:"data-[state=checked]:bg-warning-600 data-[state=unchecked]:bg-secondary-200",error:"data-[state=checked]:bg-error-600 data-[state=unchecked]:bg-secondary-200"}},defaultVariants:{size:"md",variant:"default"}}),W=(0,a.F)("pointer-events-none block rounded-full bg-white shadow-lg ring-0 transition-transform",{variants:{size:{sm:"h-3 w-3 data-[state=checked]:translate-x-3 data-[state=unchecked]:translate-x-0",md:"h-4 w-4 data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0",lg:"h-5 w-5 data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0"}},defaultVariants:{size:"md"}}),F=l().forwardRef(({className:e,size:s,variant:r,checked:a=!1,onChange:n,label:l,description:i,disabled:c,...d},x)=>{let m=()=>{!c&&n&&n(!a)};return(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("button",{type:"button",role:"switch","aria-checked":a,"data-state":a?"checked":"unchecked",onClick:m,className:(0,o.cn)(E({size:s,variant:r}),e),disabled:c,ref:x,...d,children:(0,t.jsx)("span",{"data-state":a?"checked":"unchecked",className:(0,o.cn)(W({size:s}))})}),(l||i)&&(0,t.jsxs)("div",{className:"flex flex-col",children:[l&&(0,t.jsx)("label",{className:"text-sm font-medium text-secondary-900 cursor-pointer",onClick:m,children:l}),i&&(0,t.jsx)("p",{className:"text-xs text-secondary-600",children:i})]})]})});F.displayName="Toggle"},1191:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n,vv:()=>l});var t=r(9384),a=r(2348);function n(...e){return(0,a.QP)((0,t.$)(e))}function l(e,s="USD",r="en-US"){return new Intl.NumberFormat(r,{style:"currency",currency:s}).format(e)}},4943:(e,s,r)=>{"use strict";r.d(s,{PortalLayoutContent:()=>t});let t=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call PortalLayoutContent() from the server but PortalLayoutContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx","PortalLayoutContent")},5589:(e,s,r)=>{Promise.resolve().then(r.bind(r,4943))},8342:(e,s,r)=>{"use strict";r.d(s,{PortalLayoutContent:()=>M});var t=r(687),a=r(3210),n=r(4393),l=r(1191),o=r(5814),i=r.n(o),c=r(6189);let d={"":"Home",dashboard:"Dashboard",products:"Products",orders:"Orders",contracts:"Contracts",profile:"Profile",settings:"Settings",help:"Help"},x={"":"\uD83C\uDFE0",dashboard:"\uD83D\uDCCA",products:"\uD83D\uDCF1",orders:"\uD83D\uDCCB",contracts:"\uD83D\uDCC4",profile:"\uD83D\uDC64",settings:"⚙️",help:"❓"};function m(){let e=(0,c.usePathname)(),{isRTL:s}=(0,n.o)(),r=(()=>{let s=e.split("/").filter(Boolean),r=[];r.push({label:d[""]||"Home",href:"/",icon:x[""]});let t="";return s.forEach((e,s)=>{t+=`/${e}`;let a=decodeURIComponent(e),n=d[a]||a.charAt(0).toUpperCase()+a.slice(1).replace(/-/g," ");r.push({label:n,href:t,icon:x[a]})}),r})();return r.length<=1?null:(0,t.jsx)("nav",{className:"flex items-center space-x-1 text-sm text-secondary-600 mb-6","aria-label":"Breadcrumb",children:(0,t.jsx)("ol",{className:"flex items-center space-x-1",children:r.map((e,a)=>{let n=a===r.length-1;return(0,t.jsxs)("li",{className:"flex items-center",children:[a>0&&(0,t.jsx)("svg",{className:(0,l.cn)("h-4 w-4 mx-2 text-secondary-400",s&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),n?(0,t.jsxs)("span",{className:"flex items-center space-x-1 text-secondary-900 font-medium",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]}):(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-1 hover:text-secondary-900 transition-colors",children:[e.icon&&(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]})]},e.href)})})})}var h=r(665),u=r(3710),f=r(522);function p(){let e=(0,f.jL)(),{items:s,totalItems:r,totalAmount:a,currency:n}=(0,f.GV)(e=>e.cart),o=s=>{e((0,u.dt)(s))},i=(s,r)=>{e((0,u.RO)({itemId:s,quantity:r}))},c=[...s.map(e=>({key:e.id,label:(0,t.jsx)("div",{className:"w-80 p-2",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-secondary-100 rounded flex items-center justify-center",children:"\uD83D\uDCF1"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-secondary-900 truncate",children:e.product.name}),(0,t.jsx)("div",{className:"text-xs text-secondary-500",children:(0,l.vv)(e.product.price,n)}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),i(e.id,Math.max(1,e.quantity-1))},className:"w-6 h-6 rounded border border-secondary-300 flex items-center justify-center text-xs hover:bg-secondary-50",children:"-"}),(0,t.jsx)("span",{className:"text-xs font-medium w-8 text-center",children:e.quantity}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),i(e.id,e.quantity+1)},className:"w-6 h-6 rounded border border-secondary-300 flex items-center justify-center text-xs hover:bg-secondary-50",children:"+"}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),o(e.id)},className:"text-error-600 hover:text-error-700 text-xs ml-2",children:"Remove"})]})]})]})}),onClick:()=>{}})),...s.length>0?[{key:"divider",label:(0,t.jsx)("div",{className:"border-t border-secondary-200 my-2"}),onClick:()=>{}},{key:"total",label:(0,t.jsx)("div",{className:"p-2",children:(0,t.jsxs)("div",{className:"flex justify-between items-center font-medium",children:[(0,t.jsx)("span",{children:"Total:"}),(0,t.jsx)("span",{children:(0,l.vv)(a,n)})]})}),onClick:()=>{}},{key:"checkout",label:(0,t.jsx)("div",{className:"p-2",children:(0,t.jsx)(h.$n,{fullWidth:!0,size:"sm",children:"Checkout"})}),onClick:()=>{window.location.href="/checkout"}}]:[{key:"empty",label:(0,t.jsx)("div",{className:"p-4 text-center text-secondary-500",children:"Your cart is empty"}),onClick:()=>{}}]];return(0,t.jsx)(h.ms,{trigger:(0,t.jsxs)(h.$n,{variant:"ghost",size:"sm",className:"relative",children:[(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6M20 13v6a2 2 0 01-2 2H6a2 2 0 01-2-2v-6m16 0V9a2 2 0 00-2-2H6a2 2 0 00-2-2v4"})}),r>0&&(0,t.jsx)(h.Ex,{variant:"error",size:"sm",className:"absolute -top-1 -right-1 min-w-5 h-5 flex items-center justify-center text-xs",children:r})]}),items:c,align:"right",menuClassName:"max-h-96 overflow-y-auto"})}let b=[{code:"en",name:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},{code:"ar",name:"العربية",flag:"\uD83C\uDDF8\uD83C\uDDE6"}];function g(){let{locale:e,setLocale:s}=(0,n.o)(),r=b.find(s=>s.code===e)||b[0],a=b.map(e=>({key:e.code,label:`${e.flag} ${e.name}`,onClick:()=>s(e.code)}));return(0,t.jsx)(h.ms,{trigger:(0,t.jsxs)(h.$n,{variant:"ghost",size:"sm",className:"gap-2",children:[(0,t.jsx)("span",{children:r.flag}),(0,t.jsx)("span",{className:"hidden sm:inline",children:r.name}),(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),items:a,align:"right"})}function y(){let[e,s]=(0,a.useState)(""),[r,n]=(0,a.useState)(!1),[l,o]=(0,a.useState)([]),i=(0,a.useRef)(null),{items:c}=(0,f.GV)(e=>e.products),d=e=>{s(e)},x=e=>{s(e.title),n(!1),"product"===e.type&&(window.location.href=`/products/${e.id}`)};return(0,t.jsxs)("div",{className:"relative w-full max-w-md",ref:i,children:[(0,t.jsx)(h.pd,{placeholder:"Search products...",value:e,onChange:e=>d(e.target.value),leftIcon:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})}),className:"pr-10"}),e&&(0,t.jsx)("button",{onClick:()=>{s(""),n(!1)},className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600",children:(0,t.jsx)("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),r&&l.length>0&&(0,t.jsxs)("div",{className:"absolute z-50 w-full mt-1 bg-white border border-secondary-300 rounded-lg shadow-lg max-h-80 overflow-auto",children:[(0,t.jsx)("div",{className:"py-2",children:l.map(e=>(0,t.jsx)("button",{onClick:()=>x(e),className:"w-full px-4 py-3 text-left hover:bg-secondary-50 focus:bg-secondary-50 focus:outline-none",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-secondary-100 rounded flex items-center justify-center",children:"product"===e.type?"\uD83D\uDCF1":"\uD83D\uDCC1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-secondary-900",children:e.title}),(0,t.jsx)("div",{className:"text-xs text-secondary-500 capitalize",children:e.type})]})]}),e.price&&(0,t.jsxs)("div",{className:"text-sm font-medium text-primary-600",children:["$",e.price.toLocaleString()]})]})},e.id))}),e&&(0,t.jsx)("div",{className:"border-t border-secondary-200 px-4 py-2",children:(0,t.jsxs)("button",{onClick:()=>{window.location.href=`/products?search=${encodeURIComponent(e)}`,n(!1)},className:"text-sm text-primary-600 hover:text-primary-700",children:['Search for "',e,'"']})})]})]})}var v=r(1188);let j={light:"☀️",dark:"\uD83C\uDF19",system:"\uD83D\uDCBB"},N={light:"Light",dark:"Dark",system:"System"};function w(){let{theme:e,toggleTheme:s}=(0,v.D)();return(0,t.jsxs)(h.$n,{variant:"ghost",size:"sm",onClick:s,className:"gap-2",title:`Current theme: ${N[e]}`,children:[(0,t.jsx)("span",{children:j[e]}),(0,t.jsx)("span",{className:"hidden sm:inline",children:N[e]})]})}let k=[{href:"/products",label:"Products",icon:"\uD83D\uDCF1"},{href:"/orders",label:"Orders",icon:"\uD83D\uDCCB"},{href:"/contracts",label:"Contracts",icon:"\uD83D\uDCC4"},{href:"/dashboard",label:"Dashboard",icon:"\uD83D\uDCCA"}],C=[{key:"profile",label:"My Profile",icon:"\uD83D\uDC64"},{key:"settings",label:"Settings",icon:"⚙️"},{key:"help",label:"Help & Support",icon:"❓"},{key:"logout",label:"Sign Out",icon:"\uD83D\uDEAA",danger:!0}];function L(){let[e,s]=(0,a.useState)(!1),{isRTL:r}=(0,n.o)();return(0,t.jsxs)("header",{className:"bg-white shadow-sm border-b border-secondary-200 sticky top-0 z-40",children:[(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center",children:(0,t.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-lg",children:"E"})}),(0,t.jsx)("span",{className:"text-xl font-bold text-secondary-900 hidden sm:block",children:"ElectroShop"})]})}),(0,t.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:k.map(e=>(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-2 text-secondary-600 hover:text-secondary-900 transition-colors",children:[(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.href))}),(0,t.jsx)("div",{className:"flex-1 max-w-lg mx-8 hidden lg:block",children:(0,t.jsx)(y,{})}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(h.$n,{variant:"ghost",size:"sm",className:"lg:hidden",onClick:()=>{s(!e)},children:(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),(0,t.jsx)(w,{}),(0,t.jsx)(g,{}),(0,t.jsx)(p,{}),(0,t.jsx)(h.ms,{trigger:(0,t.jsx)(h.$n,{variant:"ghost",size:"sm",className:"relative",children:(0,t.jsx)("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-primary-600 font-medium",children:"U"})})}),items:C,align:"right"}),(0,t.jsx)(h.$n,{variant:"ghost",size:"sm",className:"md:hidden",onClick:()=>s(!e),children:(0,t.jsx)("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})})]})]}),(0,t.jsx)("div",{className:"lg:hidden pb-4",children:(0,t.jsx)(y,{})})]}),e&&(0,t.jsx)("div",{className:"md:hidden bg-white border-t border-secondary-200",children:(0,t.jsx)("div",{className:"px-4 py-2 space-y-1",children:k.map(e=>(0,t.jsxs)(i(),{href:e.href,className:"flex items-center space-x-3 px-3 py-2 rounded-lg text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50 transition-colors",onClick:()=>s(!1),children:[(0,t.jsx)("span",{children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.href))})})]})}let z=[{id:"dashboard",label:"Dashboard",href:"/dashboard",icon:"\uD83D\uDCCA"},{id:"products",label:"Products",href:"/products",icon:"\uD83D\uDCF1",children:[{id:"all-products",label:"All Products",href:"/products",icon:"\uD83D\uDCE6"},{id:"smartphones",label:"Smartphones",href:"/products?category=smartphones",icon:"\uD83D\uDCF1"},{id:"laptops",label:"Laptops",href:"/products?category=laptops",icon:"\uD83D\uDCBB"},{id:"tablets",label:"Tablets",href:"/products?category=tablets",icon:"\uD83D\uDCF1"},{id:"accessories",label:"Accessories",href:"/products?category=accessories",icon:"\uD83C\uDFA7"}]},{id:"orders",label:"Orders",href:"/orders",icon:"\uD83D\uDCCB",badge:"3",children:[{id:"all-orders",label:"All Orders",href:"/orders",icon:"\uD83D\uDCCB"},{id:"pending",label:"Pending",href:"/orders?status=pending",icon:"⏳"},{id:"processing",label:"Processing",href:"/orders?status=processing",icon:"⚙️"},{id:"shipped",label:"Shipped",href:"/orders?status=shipped",icon:"\uD83D\uDE9A"},{id:"delivered",label:"Delivered",href:"/orders?status=delivered",icon:"✅"}]},{id:"contracts",label:"Contracts",href:"/contracts",icon:"\uD83D\uDCC4"},{id:"profile",label:"My Account",href:"/profile",icon:"\uD83D\uDC64",children:[{id:"profile-info",label:"Profile Info",href:"/profile",icon:"\uD83D\uDC64"},{id:"company",label:"Company Details",href:"/profile/company",icon:"\uD83C\uDFE2"},{id:"preferences",label:"Preferences",href:"/profile/preferences",icon:"⚙️"},{id:"security",label:"Security",href:"/profile/security",icon:"\uD83D\uDD12"}]}];function R({isCollapsed:e=!1,onToggleCollapse:s}){let r=(0,c.usePathname)(),{isRTL:o}=(0,n.o)(),[d,x]=(0,a.useState)(["products","orders"]),m=e=>{x(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},u=e=>"/"===e?"/"===r:r.startsWith(e),f=(s,r=0)=>{let a=s.children&&s.children.length>0,n=d.includes(s.id),c=u(s.href);return(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsxs)(i(),{href:s.href,className:(0,l.cn)("flex items-center flex-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors",r>0&&"ml-6",c?"bg-primary-100 text-primary-700":"text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50"),children:[(0,t.jsx)("span",{className:"flex-shrink-0",children:s.icon}),!e&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:(0,l.cn)("ml-3",o&&"mr-3 ml-0"),children:s.label}),s.badge&&(0,t.jsx)(h.Ex,{variant:"primary",size:"sm",className:"ml-auto",children:s.badge})]})]}),a&&!e&&(0,t.jsx)(h.$n,{variant:"ghost",size:"sm",onClick:()=>m(s.id),className:"p-1 ml-1",children:(0,t.jsx)("svg",{className:(0,l.cn)("h-4 w-4 transition-transform",n&&"rotate-90"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})})]}),a&&!e&&n&&s.children&&(0,t.jsx)("div",{className:"mt-1 space-y-1",children:s.children.map(e=>f(e,r+1))})]},s.id)};return(0,t.jsx)("div",{className:(0,l.cn)("bg-white border-r border-secondary-200 transition-all duration-300",e?"w-16":"w-64"),children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[!e&&(0,t.jsx)("h2",{className:"text-lg font-semibold text-secondary-900",children:"Navigation"}),(0,t.jsx)(h.$n,{variant:"ghost",size:"sm",onClick:s,className:"p-1",children:(0,t.jsx)("svg",{className:(0,l.cn)("h-4 w-4 transition-transform",e&&"rotate-180"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 19l-7-7 7-7m8 14l-7-7 7-7"})})})]}),(0,t.jsx)("nav",{className:"space-y-2",children:z.map(e=>f(e))}),!e&&(0,t.jsxs)("div",{className:"mt-8 pt-6 border-t border-secondary-200",children:[(0,t.jsx)("h3",{className:"text-sm font-medium text-secondary-500 mb-3",children:"Quick Actions"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(h.$n,{variant:"outline",size:"sm",fullWidth:!0,className:"justify-start",children:[(0,t.jsx)("span",{className:"mr-2",children:"➕"}),"New Order"]}),(0,t.jsxs)(h.$n,{variant:"outline",size:"sm",fullWidth:!0,className:"justify-start",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83D\uDCDE"}),"Contact Support"]})]})]})]})})}function M({children:e}){let[s,r]=(0,a.useState)(!1),{isRTL:o}=(0,n.o)();return(0,t.jsxs)("div",{className:(0,l.cn)("min-h-screen bg-gray-50",o&&"rtl"),children:[(0,t.jsx)(L,{}),(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("aside",{className:"hidden lg:block",children:(0,t.jsx)(R,{isCollapsed:s,onToggleCollapse:()=>r(!s)})}),(0,t.jsx)("main",{className:"flex-1 min-h-screen",children:(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)(m,{}),(0,t.jsx)("div",{className:"max-w-full",children:e})]})})]}),(0,t.jsx)("footer",{className:"bg-white border-t border-secondary-200 mt-auto",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Company"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"About Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Careers"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Press"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Blog"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Support"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Help Center"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Contact Us"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Returns"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Shipping Info"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Legal"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Privacy Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Terms of Service"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Cookie Policy"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"GDPR"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-sm font-semibold text-secondary-900 mb-4",children:"Connect"}),(0,t.jsxs)("ul",{className:"space-y-2 text-sm text-secondary-600",children:[(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Twitter"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"LinkedIn"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Facebook"})}),(0,t.jsx)("li",{children:(0,t.jsx)("a",{href:"#",className:"hover:text-secondary-900",children:"Instagram"})})]})]})]}),(0,t.jsx)("div",{className:"mt-8 pt-8 border-t border-secondary-200",children:(0,t.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-6 h-6 bg-primary-600 rounded flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-bold text-sm",children:"E"})}),(0,t.jsx)("span",{className:"text-sm text-secondary-600",children:"\xa9 2024 ElectroShop. All rights reserved."})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-4 md:mt-0",children:[(0,t.jsx)("span",{className:"text-sm text-secondary-600",children:"Powered by Next.js"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDCB3"}),(0,t.jsx)("span",{className:"text-lg",children:"\uD83D\uDD12"}),(0,t.jsx)("span",{className:"text-lg",children:"✅"})]})]})]})})]})})]})}},9157:(e,s,r)=>{Promise.resolve().then(r.bind(r,8342))},9785:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(7413);r(1120);var a=r(4943);function n({children:e}){return(0,t.jsx)(a.PortalLayoutContent,{children:e})}}};