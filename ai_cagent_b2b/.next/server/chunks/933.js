exports.id=933,exports.ids=[933],exports.modules={219:(e,t,r)=>{Promise.resolve().then(r.bind(r,7552))},522:(e,t,r)=>{"use strict";r.d(t,{M_:()=>j,jL:()=>U,GV:()=>L});var i=r(9317),s=r(4864),a=r(3710);let o=(0,i.zD)("orders/fetchOrders",async()=>(await new Promise(e=>setTimeout(e,1e3)),[{id:"1",orderNumber:"ORD-2024-001",customerId:"user1",customer:{id:"user1",email:"<EMAIL>",name:"<PERSON>",role:"buyer",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},items:[{id:"1",productId:"1",product:{id:"1",name:"iPhone 15 Pro",description:"Latest iPhone with A17 Pro chip",sku:"IPH15PRO-128",price:999,currency:"USD",category:{id:"smartphones",name:"Smartphones",isActive:!0},images:["/images/iphone-15-pro.jpg"],specifications:{},isActive:!0,stock:50,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},quantity:1,unitPrice:999,totalPrice:999}],status:"confirmed",totalAmount:999,currency:"USD",shippingAddress:{street:"123 Main St",city:"New York",state:"NY",country:"USA",postalCode:"10001"},billingAddress:{street:"123 Main St",city:"New York",state:"NY",country:"USA",postalCode:"10001"},createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}])),n=(0,i.zD)("orders/createOrder",async e=>(await new Promise(e=>setTimeout(e,1500)),{id:Date.now().toString(),orderNumber:`ORD-2024-${String(Date.now()).slice(-3)}`,customerId:e.customerId||"user1",customer:e.customer||{id:"user1",email:"<EMAIL>",name:"User",role:"buyer",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},items:e.items||[],status:"pending",totalAmount:e.totalAmount||0,currency:e.currency||"USD",shippingAddress:e.shippingAddress||{street:"",city:"",state:"",country:"",postalCode:""},billingAddress:e.billingAddress||{street:"",city:"",state:"",country:"",postalCode:""},notes:e.notes,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})),d=(0,i.zD)("orders/updateOrderStatus",async({orderId:e,status:t})=>(await new Promise(e=>setTimeout(e,500)),{orderId:e,status:t})),l=(0,i.Z0)({name:"orders",initialState:{items:[],currentOrder:null,loading:!1,error:null,filter:{status:"all",dateRange:{start:null,end:null}}},reducers:{setCurrentOrder:(e,t)=>{e.currentOrder=t.payload},setStatusFilter:(e,t)=>{e.filter.status=t.payload},setDateRangeFilter:(e,t)=>{e.filter.dateRange=t.payload},clearFilters:e=>{e.filter={status:"all",dateRange:{start:null,end:null}}},addOrderNote:(e,t)=>{let{orderId:r,note:i}=t.payload,s=e.items.find(e=>e.id===r);s&&(s.notes=s.notes?`${s.notes}
${i}`:i,s.updatedAt=new Date().toISOString())}},extraReducers:e=>{e.addCase(o.pending,e=>{e.loading=!0,e.error=null}).addCase(o.fulfilled,(e,t)=>{e.loading=!1,e.items=t.payload}).addCase(o.rejected,(e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch orders"}).addCase(n.pending,e=>{e.loading=!0,e.error=null}).addCase(n.fulfilled,(e,t)=>{e.loading=!1,e.items.unshift(t.payload),e.currentOrder=t.payload}).addCase(n.rejected,(e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to create order"}).addCase(d.fulfilled,(e,t)=>{let{orderId:r,status:i}=t.payload,s=e.items.find(e=>e.id===r);s&&(s.status=i,s.updatedAt=new Date().toISOString()),e.currentOrder&&e.currentOrder.id===r&&(e.currentOrder.status=i,e.currentOrder.updatedAt=new Date().toISOString())})}}),{setCurrentOrder:c,setStatusFilter:u,setDateRangeFilter:p,clearFilters:m,addOrderNote:g}=l.actions,y=l.reducer;var h=r(2972);let A=(0,i.zD)("user/login",async e=>{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"===e.email&&"password"===e.password)return{id:"1",email:e.email,name:"Demo User",role:"buyer",company:{id:"company1",name:"Demo Electronics Corp",address:{street:"123 Business Ave",city:"Tech City",state:"CA",country:"USA",postalCode:"90210"},contactInfo:{phone:"******-0123",email:"<EMAIL>",website:"https://demoelectronics.com"},taxId:"TAX123456789",isActive:!0},avatar:"/images/avatar-placeholder.jpg",isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};throw Error("Invalid credentials")}),S=(0,i.zD)("user/logout",async()=>(await new Promise(e=>setTimeout(e,500)),!0)),f=(0,i.zD)("user/updateProfile",async e=>(await new Promise(e=>setTimeout(e,1e3)),e)),v=(0,i.zD)("user/updatePreferences",async e=>(await new Promise(e=>setTimeout(e,500)),e)),w=(0,i.Z0)({name:"user",initialState:{currentUser:null,isAuthenticated:!1,preferences:{theme:"system",language:"en",currency:"USD",notifications:{email:!0,push:!0,orderUpdates:!0,promotions:!1},privacy:{profileVisibility:"private",showEmail:!1,showPhone:!1}},loading:!1,error:null,loginAttempts:0,lastLoginAt:null},reducers:{setTheme:(e,t)=>{e.preferences.theme=t.payload},setLanguage:(e,t)=>{e.preferences.language=t.payload},setCurrency:(e,t)=>{e.preferences.currency=t.payload},updateNotificationSettings:(e,t)=>{e.preferences.notifications={...e.preferences.notifications,...t.payload}},updatePrivacySettings:(e,t)=>{e.preferences.privacy={...e.preferences.privacy,...t.payload}},clearError:e=>{e.error=null},incrementLoginAttempts:e=>{e.loginAttempts+=1},resetLoginAttempts:e=>{e.loginAttempts=0}},extraReducers:e=>{e.addCase(A.pending,e=>{e.loading=!0,e.error=null}).addCase(A.fulfilled,(e,t)=>{e.loading=!1,e.currentUser=t.payload,e.isAuthenticated=!0,e.loginAttempts=0,e.lastLoginAt=new Date().toISOString()}).addCase(A.rejected,(e,t)=>{e.loading=!1,e.error=t.error.message||"Login failed",e.loginAttempts+=1}).addCase(S.fulfilled,e=>{e.currentUser=null,e.isAuthenticated=!1,e.error=null,e.loginAttempts=0}).addCase(f.fulfilled,(e,t)=>{e.currentUser&&(e.currentUser={...e.currentUser,...t.payload,updatedAt:new Date().toISOString()})}).addCase(v.fulfilled,(e,t)=>{e.preferences={...e.preferences,...t.payload}})}}),{setTheme:P,setLanguage:C,setCurrency:D,updateNotificationSettings:O,updatePrivacySettings:I,clearError:b,incrementLoginAttempts:x,resetLoginAttempts:R}=w.actions,T=w.reducer,j=(0,i.U1)({reducer:{products:h.Ay,cart:a.Ay,orders:y,user:T},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST","persist/REHYDRATE"]}}),devTools:!1}),U=()=>(0,s.wA)(),L=s.d4},1135:()=>{},1188:(e,t,r)=>{"use strict";r.d(t,{D:()=>n,N:()=>o});var i=r(687),s=r(3210);let a=(0,s.createContext)(void 0);function o({children:e}){let[t,r]=(0,s.useState)("system"),[o,n]=(0,s.useState)("light"),d=e=>{r(e),localStorage.setItem("theme",e)};return(0,i.jsx)(a.Provider,{value:{theme:t,actualTheme:o,setTheme:d,toggleTheme:()=>{"light"===t?d("dark"):"dark"===t?d("system"):d("light")}},children:e})}function n(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},2972:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>h,Ri:()=>l,j0:()=>s});var i=r(9317);let s=(0,i.zD)("products/fetchProducts",async()=>(await new Promise(e=>setTimeout(e,1e3)),[{id:"1",name:"iPhone 15 Pro",description:"Latest iPhone with A17 Pro chip and titanium design",sku:"IPH15PRO-128",price:999,currency:"USD",category:{id:"smartphones",name:"Smartphones",isActive:!0},images:["/images/iphone-15-pro.jpg"],specifications:{storage:"128GB",color:"Natural Titanium",display:"6.1-inch Super Retina XDR",chip:"A17 Pro"},isActive:!0,stock:50,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",name:'MacBook Pro 14"',description:"Powerful laptop with M3 chip for professionals",sku:"MBP14-M3-512",price:1999,currency:"USD",category:{id:"laptops",name:"Laptops",isActive:!0},images:["/images/macbook-pro-14.jpg"],specifications:{processor:"Apple M3",memory:"16GB",storage:"512GB SSD",display:"14.2-inch Liquid Retina XDR"},isActive:!0,stock:25,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"3",name:"iPad Air",description:"Versatile tablet with M2 chip and Apple Pencil support",sku:"IPAD-AIR-M2-256",price:749,currency:"USD",category:{id:"tablets",name:"Tablets",isActive:!0},images:["/images/ipad-air.jpg"],specifications:{chip:"Apple M2",storage:"256GB",display:"10.9-inch Liquid Retina",connectivity:"Wi-Fi + Cellular"},isActive:!0,stock:40,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}])),a=(0,i.zD)("products/fetchCategories",async()=>(await new Promise(e=>setTimeout(e,500)),[{id:"smartphones",name:"Smartphones",isActive:!0},{id:"laptops",name:"Laptops",isActive:!0},{id:"tablets",name:"Tablets",isActive:!0},{id:"accessories",name:"Accessories",isActive:!0},{id:"audio",name:"Audio",isActive:!0}])),o=(0,i.Z0)({name:"products",initialState:{items:[],categories:[],filteredItems:[],selectedCategory:null,searchQuery:"",sortBy:"name",sortOrder:"asc",priceRange:[0,1e4],loading:!1,error:null,currentProduct:null},reducers:{setSearchQuery:(e,t)=>{e.searchQuery=t.payload,n(e)},setSelectedCategory:(e,t)=>{e.selectedCategory=t.payload,n(e)},setSortBy:(e,t)=>{e.sortBy=t.payload,d(e)},setSortOrder:(e,t)=>{e.sortOrder=t.payload,d(e)},setPriceRange:(e,t)=>{e.priceRange=t.payload,n(e)},setCurrentProduct:(e,t)=>{e.currentProduct=t.payload},clearFilters:e=>{e.searchQuery="",e.selectedCategory=null,e.priceRange=[0,1e4],e.sortBy="name",e.sortOrder="asc",e.filteredItems=e.items}},extraReducers:e=>{e.addCase(s.pending,e=>{e.loading=!0,e.error=null}).addCase(s.fulfilled,(e,t)=>{e.loading=!1,e.items=t.payload,e.filteredItems=t.payload}).addCase(s.rejected,(e,t)=>{e.loading=!1,e.error=t.error.message||"Failed to fetch products"}).addCase(a.fulfilled,(e,t)=>{e.categories=t.payload})}});function n(e){let t=e.items;e.searchQuery&&(t=t.filter(t=>t.name.toLowerCase().includes(e.searchQuery.toLowerCase())||t.description.toLowerCase().includes(e.searchQuery.toLowerCase()))),e.selectedCategory&&(t=t.filter(t=>t.category.id===e.selectedCategory)),t=t.filter(t=>t.price>=e.priceRange[0]&&t.price<=e.priceRange[1]),e.filteredItems=t,d(e)}function d(e){e.filteredItems.sort((t,r)=>{let i=0;switch(e.sortBy){case"name":i=t.name.localeCompare(r.name);break;case"price":i=t.price-r.price;break;case"newest":i=new Date(r.createdAt).getTime()-new Date(t.createdAt).getTime();break;default:i=0}return"desc"===e.sortOrder?-i:i})}let{setSearchQuery:l,setSelectedCategory:c,setSortBy:u,setSortOrder:p,setPriceRange:m,setCurrentProduct:g,clearFilters:y}=o.actions,h=o.reducer},3622:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>i});let i=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Providers.tsx","Providers")},3710:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>y,RO:()=>n,bE:()=>a,dt:()=>o});let i=(0,r(9317).Z0)({name:"cart",initialState:{items:[],isOpen:!1,totalItems:0,totalAmount:0,currency:"USD",shippingCost:0,taxRate:.08,discountAmount:0},reducers:{addToCart:(e,t)=>{let{product:r,quantity:i=1,variant:a}=t.payload,o=e.items.findIndex(e=>e.product.id===r.id&&JSON.stringify(e.selectedVariant)===JSON.stringify(a));if(o>=0){let t=e.items[o];t&&(t.quantity+=i)}else{let t={id:`${r.id}-${Date.now()}`,product:r,quantity:i,selectedVariant:a,addedAt:new Date().toISOString()};e.items.push(t)}s(e)},removeFromCart:(e,t)=>{e.items=e.items.filter(e=>e.id!==t.payload),s(e)},updateQuantity:(e,t)=>{let{itemId:r,quantity:i}=t.payload,a=e.items.find(e=>e.id===r);a&&(i<=0?e.items=e.items.filter(e=>e.id!==r):a.quantity=i),s(e)},clearCart:e=>{e.items=[],e.totalItems=0,e.totalAmount=0,e.discountAmount=0,e.discountCode=void 0},toggleCart:e=>{e.isOpen=!e.isOpen},openCart:e=>{e.isOpen=!0},closeCart:e=>{e.isOpen=!1},applyDiscountCode:(e,t)=>{let r=t.payload.toUpperCase(),i={WELCOME10:.1,SAVE20:.2,STUDENT15:.15};i[r]&&(e.discountCode=r,e.discountAmount=e.totalAmount*i[r])},removeDiscountCode:e=>{e.discountCode=void 0,e.discountAmount=0},setShippingCost:(e,t)=>{e.shippingCost=t.payload,s(e)}}});function s(e){e.totalItems=e.items.reduce((e,t)=>e+t.quantity,0);let t=e.items.reduce((e,t)=>e+t.product.price*t.quantity,0),r=t*e.taxRate;e.totalAmount=t+r+e.shippingCost-e.discountAmount}let{addToCart:a,removeFromCart:o,updateQuantity:n,clearCart:d,toggleCart:l,openCart:c,closeCart:u,applyDiscountCode:p,removeDiscountCode:m,setShippingCost:g}=i.actions,y=i.reducer},4393:(e,t,r)=>{"use strict";r.d(t,{I:()=>n,o:()=>d});var i=r(687),s=r(3210);let a=(0,s.createContext)(void 0),o=["ar"];function n({children:e}){let[t,r]=(0,s.useState)("en"),n=o.includes(t),d=e=>{r(e),localStorage.setItem("locale",e)};return(0,i.jsx)(a.Provider,{value:{locale:t,setLocale:d,isRTL:n,direction:n?"rtl":"ltr",toggleLanguage:()=>{d("en"===t?"ar":"en")}},children:e})}function d(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useLanguage must be used within a LanguageProvider");return e}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>n,viewport:()=>d});var i=r(7413),s=r(7339),a=r.n(s),o=r(3622);r(1135);let n={title:"B2B Portal",description:"Next.js B2B E-commerce Portal",keywords:["B2B","E-commerce","Portal","Next.js"],authors:[{name:"B2B Portal Team"}],robots:"index, follow"},d={width:"device-width",initialScale:1};function l({children:e}){return(0,i.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,i.jsx)("body",{className:a().className,children:(0,i.jsx)(o.Providers,{children:(0,i.jsx)("div",{id:"root",children:e})})})})}},4563:(e,t,r)=>{Promise.resolve().then(r.bind(r,3622))},7552:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>d});var i=r(687);r(3210);var s=r(4864),a=r(4393),o=r(1188),n=r(522);function d({children:e}){return(0,i.jsx)(s.Kq,{store:n.M_,children:(0,i.jsx)(o.N,{children:(0,i.jsx)(a.I,{children:e})})})}},8992:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},9264:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))}};