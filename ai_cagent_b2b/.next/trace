[{"name": "generate-buildid", "duration": 93, "timestamp": 701513335775, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751533513744, "traceId": "a7439055119a5a34"}, {"name": "load-custom-routes", "duration": 599, "timestamp": 701513335899, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751533513744, "traceId": "a7439055119a5a34"}, {"name": "create-dist-dir", "duration": 146, "timestamp": 701513359918, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751533513768, "traceId": "a7439055119a5a34"}, {"name": "create-pages-mapping", "duration": 85, "timestamp": 701513364489, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751533513773, "traceId": "a7439055119a5a34"}, {"name": "collect-app-paths", "duration": 1051, "timestamp": 701513364586, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751533513773, "traceId": "a7439055119a5a34"}, {"name": "create-app-mapping", "duration": 555, "timestamp": 701513365655, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751533513774, "traceId": "a7439055119a5a34"}, {"name": "public-dir-conflict-check", "duration": 271, "timestamp": 701513366358, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751533513775, "traceId": "a7439055119a5a34"}, {"name": "generate-routes-manifest", "duration": 1058, "timestamp": 701513366778, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751533513775, "traceId": "a7439055119a5a34"}, {"name": "create-entrypoints", "duration": 33603, "timestamp": 701513371339, "id": 14, "parentId": 1, "tags": {}, "startTime": 1751533513780, "traceId": "a7439055119a5a34"}, {"name": "generate-webpack-config", "duration": 134243, "timestamp": 701513404967, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751533513813, "traceId": "a7439055119a5a34"}, {"name": "next-trace-entrypoint-plugin", "duration": 1221, "timestamp": 701513583435, "id": 17, "parentId": 16, "tags": {}, "startTime": 1751533513992, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 116936, "timestamp": 701513587580, "id": 20, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 120618, "timestamp": 701513587601, "id": 22, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 148540, "timestamp": 701513587595, "id": 21, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160542, "timestamp": 701513587388, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160330, "timestamp": 701513587607, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fcontracts%2Fpage&name=app%2Fcontracts%2Fpage&pagePath=private-next-app-dir%2Fcontracts%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Fcontracts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160327, "timestamp": 701513587615, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Forders%2Fpage&name=app%2Forders%2Fpage&pagePath=private-next-app-dir%2Forders%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Forders%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160324, "timestamp": 701513587620, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fdashboard%2Fpage&name=app%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160312, "timestamp": 701513587633, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fproducts%2Fpage&name=app%2Fproducts%2Fpage&pagePath=private-next-app-dir%2Fproducts%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Fproducts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160165, "timestamp": 701513587783, "id": 28, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fprofile%2Fpage&name=app%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 160331, "timestamp": 701513587625, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751533513996, "traceId": "a7439055119a5a34"}, {"name": "build-module-tsx", "duration": 12057, "timestamp": 701513846259, "id": 69, "parentId": 16, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx", "layer": "ssr"}, "startTime": 1751533514254, "traceId": "a7439055119a5a34"}, {"name": "make", "duration": 273507, "timestamp": 701513587250, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751533513995, "traceId": "a7439055119a5a34"}, {"name": "get-entries", "duration": 430, "timestamp": 701513861394, "id": 71, "parentId": 70, "tags": {}, "startTime": 1751533514270, "traceId": "a7439055119a5a34"}, {"name": "node-file-trace-plugin", "duration": 30964, "timestamp": 701513863610, "id": 72, "parentId": 70, "tags": {"traceEntryCount": "16"}, "startTime": 1751533514272, "traceId": "a7439055119a5a34"}, {"name": "collect-traced-files", "duration": 372, "timestamp": 701513894584, "id": 73, "parentId": 70, "tags": {}, "startTime": 1751533514303, "traceId": "a7439055119a5a34"}, {"name": "finish-modules", "duration": 33645, "timestamp": 701513861314, "id": 70, "parentId": 17, "tags": {}, "startTime": 1751533514270, "traceId": "a7439055119a5a34"}, {"name": "chunk-graph", "duration": 6833, "timestamp": 701513906188, "id": 75, "parentId": 74, "tags": {}, "startTime": 1751533514314, "traceId": "a7439055119a5a34"}, {"name": "optimize-modules", "duration": 10, "timestamp": 701513913085, "id": 77, "parentId": 74, "tags": {}, "startTime": 1751533514321, "traceId": "a7439055119a5a34"}, {"name": "optimize-chunks", "duration": 4925, "timestamp": 701513913132, "id": 78, "parentId": 74, "tags": {}, "startTime": 1751533514321, "traceId": "a7439055119a5a34"}, {"name": "optimize-tree", "duration": 163, "timestamp": 701513918113, "id": 79, "parentId": 74, "tags": {}, "startTime": 1751533514326, "traceId": "a7439055119a5a34"}, {"name": "optimize-chunk-modules", "duration": 3956, "timestamp": 701513918349, "id": 80, "parentId": 74, "tags": {}, "startTime": 1751533514327, "traceId": "a7439055119a5a34"}, {"name": "optimize", "duration": 9292, "timestamp": 701513913062, "id": 76, "parentId": 74, "tags": {}, "startTime": 1751533514321, "traceId": "a7439055119a5a34"}, {"name": "module-hash", "duration": 5837, "timestamp": 701513929774, "id": 81, "parentId": 74, "tags": {}, "startTime": 1751533514338, "traceId": "a7439055119a5a34"}, {"name": "code-generation", "duration": 1720, "timestamp": 701513935635, "id": 82, "parentId": 74, "tags": {}, "startTime": 1751533514344, "traceId": "a7439055119a5a34"}, {"name": "hash", "duration": 3896, "timestamp": 701513939326, "id": 83, "parentId": 74, "tags": {}, "startTime": 1751533514348, "traceId": "a7439055119a5a34"}, {"name": "code-generation-jobs", "duration": 117, "timestamp": 701513943221, "id": 84, "parentId": 74, "tags": {}, "startTime": 1751533514351, "traceId": "a7439055119a5a34"}, {"name": "module-assets", "duration": 129, "timestamp": 701513943316, "id": 85, "parentId": 74, "tags": {}, "startTime": 1751533514352, "traceId": "a7439055119a5a34"}, {"name": "create-chunk-assets", "duration": 1997, "timestamp": 701513943449, "id": 86, "parentId": 74, "tags": {}, "startTime": 1751533514352, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 177, "timestamp": 701513949059, "id": 88, "parentId": 87, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 118, "timestamp": 701513949122, "id": 89, "parentId": 87, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 113, "timestamp": 701513949128, "id": 90, "parentId": 87, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 110, "timestamp": 701513949131, "id": 91, "parentId": 87, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 108, "timestamp": 701513949134, "id": 92, "parentId": 87, "tags": {"name": "../app/contracts/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 100, "timestamp": 701513949143, "id": 93, "parentId": 87, "tags": {"name": "../app/orders/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 97, "timestamp": 701513949146, "id": 94, "parentId": 87, "tags": {"name": "../app/dashboard/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 95, "timestamp": 701513949149, "id": 95, "parentId": 87, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 93, "timestamp": 701513949151, "id": 96, "parentId": 87, "tags": {"name": "../app/products/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 30, "timestamp": 701513949215, "id": 97, "parentId": 87, "tags": {"name": "../app/profile/page.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 22, "timestamp": 701513949223, "id": 98, "parentId": 87, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 15, "timestamp": 701513949230, "id": 99, "parentId": 87, "tags": {"name": "150.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 13, "timestamp": 701513949233, "id": 100, "parentId": 87, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 11, "timestamp": 701513949235, "id": 101, "parentId": 87, "tags": {"name": "933.js", "cache": "HIT"}, "startTime": 1751533514357, "traceId": "a7439055119a5a34"}, {"name": "minify-webpack-plugin-optimize", "duration": 2460, "timestamp": 701513946790, "id": 87, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751533514355, "traceId": "a7439055119a5a34"}, {"name": "css-minimizer-plugin", "duration": 58, "timestamp": 701513949310, "id": 102, "parentId": 16, "tags": {}, "startTime": 1751533514358, "traceId": "a7439055119a5a34"}, {"name": "create-trace-assets", "duration": 917, "timestamp": 701513949461, "id": 103, "parentId": 17, "tags": {}, "startTime": 1751533514358, "traceId": "a7439055119a5a34"}, {"name": "seal", "duration": 52888, "timestamp": 701513900758, "id": 74, "parentId": 16, "tags": {}, "startTime": 1751533514309, "traceId": "a7439055119a5a34"}, {"name": "webpack-compilation", "duration": 376451, "timestamp": 701513582236, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751533513990, "traceId": "a7439055119a5a34"}, {"name": "emit", "duration": 3064, "timestamp": 701513958968, "id": 104, "parentId": 13, "tags": {}, "startTime": 1751533514367, "traceId": "a7439055119a5a34"}, {"name": "webpack-close", "duration": 35072, "timestamp": 701513962594, "id": 105, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751533514371, "traceId": "a7439055119a5a34"}, {"name": "webpack-generate-error-stats", "duration": 1169, "timestamp": 701513997701, "id": 106, "parentId": 105, "tags": {}, "startTime": 1751533514406, "traceId": "a7439055119a5a34"}, {"name": "make", "duration": 107, "timestamp": 701514003012, "id": 108, "parentId": 107, "tags": {}, "startTime": 1751533514411, "traceId": "a7439055119a5a34"}, {"name": "chunk-graph", "duration": 19, "timestamp": 701514003401, "id": 110, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "optimize-modules", "duration": 3, "timestamp": 701514003438, "id": 112, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "optimize-chunks", "duration": 26, "timestamp": 701514003466, "id": 113, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "optimize-tree", "duration": 3, "timestamp": 701514003507, "id": 114, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "optimize-chunk-modules", "duration": 21, "timestamp": 701514003536, "id": 115, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "optimize", "duration": 142, "timestamp": 701514003428, "id": 111, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "module-hash", "duration": 6, "timestamp": 701514003653, "id": 116, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "code-generation", "duration": 8, "timestamp": 701514003663, "id": 117, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "hash", "duration": 27, "timestamp": 701514003693, "id": 118, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "code-generation-jobs", "duration": 63, "timestamp": 701514003721, "id": 119, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "module-assets", "duration": 55, "timestamp": 701514003732, "id": 120, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "create-chunk-assets", "duration": 7, "timestamp": 701514003791, "id": 121, "parentId": 109, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "minify-js", "duration": 15, "timestamp": 701514007228, "id": 123, "parentId": 122, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751533514415, "traceId": "a7439055119a5a34"}, {"name": "minify-webpack-plugin-optimize", "duration": 1054, "timestamp": 701514006195, "id": 122, "parentId": 107, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751533514414, "traceId": "a7439055119a5a34"}, {"name": "css-minimizer-plugin", "duration": 4, "timestamp": 701514007278, "id": 124, "parentId": 107, "tags": {}, "startTime": 1751533514416, "traceId": "a7439055119a5a34"}, {"name": "seal", "duration": 4944, "timestamp": 701514003342, "id": 109, "parentId": 107, "tags": {}, "startTime": 1751533514412, "traceId": "a7439055119a5a34"}, {"name": "webpack-compilation", "duration": 6062, "timestamp": 701514002264, "id": 107, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751533514410, "traceId": "a7439055119a5a34"}, {"name": "emit", "duration": 395, "timestamp": 701514008348, "id": 125, "parentId": 13, "tags": {}, "startTime": 1751533514417, "traceId": "a7439055119a5a34"}, {"name": "webpack-close", "duration": 76, "timestamp": 701514008874, "id": 126, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751533514417, "traceId": "a7439055119a5a34"}, {"name": "webpack-generate-error-stats", "duration": 265, "timestamp": 701514008953, "id": 127, "parentId": 126, "tags": {}, "startTime": 1751533514417, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 18319, "timestamp": 701514012861, "id": 138, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 18323, "timestamp": 701514012866, "id": 139, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 18322, "timestamp": 701514012868, "id": 140, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 18320, "timestamp": 701514012871, "id": 143, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 78899, "timestamp": 701514012853, "id": 133, "parentId": 129, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 99522, "timestamp": 701514012854, "id": 134, "parentId": 129, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "build-module-tsx", "duration": 7647, "timestamp": 701514106701, "id": 146, "parentId": 128, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/ui/Input/index.tsx", "layer": "app-pages-browser"}, "startTime": 1751533514515, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 112538, "timestamp": 701514012858, "id": 136, "parentId": 129, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 116366, "timestamp": 701514012870, "id": 142, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp%2Fproducts%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 364355, "timestamp": 701514012851, "id": 132, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 364548, "timestamp": 701514012845, "id": 131, "parentId": 129, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 364528, "timestamp": 701514012869, "id": 141, "parentId": 129, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "postcss-process", "duration": 231733, "timestamp": 701514216488, "id": 148, "parentId": 147, "tags": {}, "startTime": 1751533514625, "traceId": "a7439055119a5a34"}, {"name": "postcss-loader", "duration": 333744, "timestamp": 701514114520, "id": 147, "parentId": 145, "tags": {}, "startTime": 1751533514523, "traceId": "a7439055119a5a34"}, {"name": "css-loader", "duration": 16081, "timestamp": 701514448362, "id": 149, "parentId": 145, "tags": {"astUsed": "true"}, "startTime": 1751533514857, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 454817, "timestamp": 701514012856, "id": 135, "parentId": 129, "tags": {"request": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/node_modules/next/dist/client/router.js"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "add-entry", "duration": 454851, "timestamp": 701514012826, "id": 130, "parentId": 129, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1751533514421, "traceId": "a7439055119a5a34"}, {"name": "build-module-css", "duration": 374574, "timestamp": 701514093482, "id": 145, "parentId": 144, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/globals.css.webpack[javascript/auto]!=!/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/globals.css", "layer": null}, "startTime": 1751533514502, "traceId": "a7439055119a5a34"}, {"name": "build-module-css", "duration": 397563, "timestamp": 701514073035, "id": 144, "parentId": 128, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/app/globals.css", "layer": "app-pages-browser"}, "startTime": 1751533514481, "traceId": "a7439055119a5a34"}]