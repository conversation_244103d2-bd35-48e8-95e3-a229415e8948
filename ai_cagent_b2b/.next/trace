[{"name": "generate-buildid", "duration": 106, "timestamp": 703342639494, "id": 4, "parentId": 1, "tags": {}, "startTime": 1751535343004, "traceId": "20e66b668228180c"}, {"name": "load-custom-routes", "duration": 662, "timestamp": 703342639638, "id": 5, "parentId": 1, "tags": {}, "startTime": 1751535343005, "traceId": "20e66b668228180c"}, {"name": "create-dist-dir", "duration": 125, "timestamp": 703342665838, "id": 6, "parentId": 1, "tags": {}, "startTime": 1751535343031, "traceId": "20e66b668228180c"}, {"name": "create-pages-mapping", "duration": 92, "timestamp": 703342673273, "id": 7, "parentId": 1, "tags": {}, "startTime": 1751535343038, "traceId": "20e66b668228180c"}, {"name": "collect-app-paths", "duration": 959, "timestamp": 703342673380, "id": 8, "parentId": 1, "tags": {}, "startTime": 1751535343038, "traceId": "20e66b668228180c"}, {"name": "create-app-mapping", "duration": 599, "timestamp": 703342674354, "id": 9, "parentId": 1, "tags": {}, "startTime": 1751535343039, "traceId": "20e66b668228180c"}, {"name": "public-dir-conflict-check", "duration": 211, "timestamp": 703342675106, "id": 10, "parentId": 1, "tags": {}, "startTime": 1751535343040, "traceId": "20e66b668228180c"}, {"name": "generate-routes-manifest", "duration": 1060, "timestamp": 703342675462, "id": 11, "parentId": 1, "tags": {}, "startTime": 1751535343040, "traceId": "20e66b668228180c"}, {"name": "create-entrypoints", "duration": 13225, "timestamp": 703342705837, "id": 14, "parentId": 1, "tags": {}, "startTime": 1751535343071, "traceId": "20e66b668228180c"}, {"name": "generate-webpack-config", "duration": 152298, "timestamp": 703342719087, "id": 15, "parentId": 13, "tags": {}, "startTime": 1751535343084, "traceId": "20e66b668228180c"}, {"name": "next-trace-entrypoint-plugin", "duration": 1244, "timestamp": 703342926558, "id": 17, "parentId": 16, "tags": {}, "startTime": 1751535343292, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 128857, "timestamp": 703342931003, "id": 20, "parentId": 18, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 131837, "timestamp": 703342931234, "id": 28, "parentId": 18, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 161940, "timestamp": 703342931018, "id": 21, "parentId": 18, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 177509, "timestamp": 703342930785, "id": 19, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 177280, "timestamp": 703342931024, "id": 22, "parentId": 18, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 9550, "timestamp": 703343111533, "id": 29, "parentId": 16, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx", "layer": "rsc"}, "startTime": 1751535343476, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 190768, "timestamp": 703342931035, "id": 23, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(portal)%2Fdashboard%2Fpage&name=app%2F(portal)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(portal)%2Fdashboard%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2F(portal)%2Fdashboard%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 190768, "timestamp": 703342931041, "id": 24, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(portal)%2Fcontracts%2Fpage&name=app%2F(portal)%2Fcontracts%2Fpage&pagePath=private-next-app-dir%2F(portal)%2Fcontracts%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2F(portal)%2Fcontracts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 190761, "timestamp": 703342931050, "id": 25, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(portal)%2Fproducts%2Fpage&name=app%2F(portal)%2Fproducts%2Fpage&pagePath=private-next-app-dir%2F(portal)%2Fproducts%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2F(portal)%2Fproducts%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 190757, "timestamp": 703342931056, "id": 26, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(portal)%2Forders%2Fpage&name=app%2F(portal)%2Forders%2Fpage&pagePath=private-next-app-dir%2F(portal)%2Forders%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2F(portal)%2Forders%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 190793, "timestamp": 703342931067, "id": 27, "parentId": 18, "tags": {"request": "next-app-loader?page=%2F(portal)%2Fprofile%2Fpage&name=app%2F(portal)%2Fprofile%2Fpage&pagePath=private-next-app-dir%2F(portal)%2Fprofile%2Fpage.tsx&appDir=%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp&appPaths=%2F(portal)%2Fprofile%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 12188, "timestamp": 703343188924, "id": 80, "parentId": 16, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx", "layer": "ssr"}, "startTime": 1751535343554, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 6760, "timestamp": 703343238526, "id": 81, "parentId": 80, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SidebarNavigation.tsx", "layer": "ssr"}, "startTime": 1751535343603, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 2501, "timestamp": 703343252736, "id": 82, "parentId": 80, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Header.tsx", "layer": "ssr"}, "startTime": 1751535343618, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 4003, "timestamp": 703343264028, "id": 83, "parentId": 82, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SearchAutocomplete.tsx", "layer": "ssr"}, "startTime": 1751535343629, "traceId": "20e66b668228180c"}, {"name": "make", "duration": 341349, "timestamp": 703342930643, "id": 18, "parentId": 16, "tags": {}, "startTime": 1751535343296, "traceId": "20e66b668228180c"}, {"name": "get-entries", "duration": 487, "timestamp": 703343272612, "id": 85, "parentId": 84, "tags": {}, "startTime": 1751535343638, "traceId": "20e66b668228180c"}, {"name": "node-file-trace-plugin", "duration": 30722, "timestamp": 703343275087, "id": 86, "parentId": 84, "tags": {"traceEntryCount": "16"}, "startTime": 1751535343640, "traceId": "20e66b668228180c"}, {"name": "collect-traced-files", "duration": 346, "timestamp": 703343305818, "id": 87, "parentId": 84, "tags": {}, "startTime": 1751535343671, "traceId": "20e66b668228180c"}, {"name": "finish-modules", "duration": 33660, "timestamp": 703343272507, "id": 84, "parentId": 17, "tags": {}, "startTime": 1751535343637, "traceId": "20e66b668228180c"}, {"name": "chunk-graph", "duration": 7531, "timestamp": 703343318174, "id": 89, "parentId": 88, "tags": {}, "startTime": 1751535343683, "traceId": "20e66b668228180c"}, {"name": "optimize-modules", "duration": 11, "timestamp": 703343325770, "id": 91, "parentId": 88, "tags": {}, "startTime": 1751535343691, "traceId": "20e66b668228180c"}, {"name": "optimize-chunks", "duration": 6925, "timestamp": 703343325813, "id": 92, "parentId": 88, "tags": {}, "startTime": 1751535343691, "traceId": "20e66b668228180c"}, {"name": "optimize-tree", "duration": 64, "timestamp": 703343332785, "id": 93, "parentId": 88, "tags": {}, "startTime": 1751535343698, "traceId": "20e66b668228180c"}, {"name": "optimize-chunk-modules", "duration": 5311, "timestamp": 703343332886, "id": 94, "parentId": 88, "tags": {}, "startTime": 1751535343698, "traceId": "20e66b668228180c"}, {"name": "optimize", "duration": 12505, "timestamp": 703343325746, "id": 90, "parentId": 88, "tags": {}, "startTime": 1751535343691, "traceId": "20e66b668228180c"}, {"name": "module-hash", "duration": 7850, "timestamp": 703343347353, "id": 95, "parentId": 88, "tags": {}, "startTime": 1751535343712, "traceId": "20e66b668228180c"}, {"name": "code-generation", "duration": 16667, "timestamp": 703343355237, "id": 96, "parentId": 88, "tags": {}, "startTime": 1751535343720, "traceId": "20e66b668228180c"}, {"name": "hash", "duration": 4507, "timestamp": 703343373799, "id": 97, "parentId": 88, "tags": {}, "startTime": 1751535343739, "traceId": "20e66b668228180c"}, {"name": "code-generation-jobs", "duration": 178, "timestamp": 703343378304, "id": 98, "parentId": 88, "tags": {}, "startTime": 1751535343743, "traceId": "20e66b668228180c"}, {"name": "module-assets", "duration": 168, "timestamp": 703343378457, "id": 99, "parentId": 88, "tags": {}, "startTime": 1751535343743, "traceId": "20e66b668228180c"}, {"name": "create-chunk-assets", "duration": 1552, "timestamp": 703343378630, "id": 100, "parentId": 88, "tags": {}, "startTime": 1751535343744, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3334, "timestamp": 703343384618, "id": 102, "parentId": 101, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3273, "timestamp": 703343384684, "id": 103, "parentId": 101, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3268, "timestamp": 703343384691, "id": 104, "parentId": 101, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3265, "timestamp": 703343384694, "id": 105, "parentId": 101, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3255, "timestamp": 703343384704, "id": 106, "parentId": 101, "tags": {"name": "../app/(portal)/dashboard/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3239, "timestamp": 703343384721, "id": 107, "parentId": 101, "tags": {"name": "../app/(portal)/contracts/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3236, "timestamp": 703343384724, "id": 108, "parentId": 101, "tags": {"name": "../app/(portal)/products/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3234, "timestamp": 703343384727, "id": 109, "parentId": 101, "tags": {"name": "../app/(portal)/orders/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3232, "timestamp": 703343384730, "id": 110, "parentId": 101, "tags": {"name": "../app/(portal)/profile/page.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3180, "timestamp": 703343384782, "id": 111, "parentId": 101, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3134, "timestamp": 703343384828, "id": 112, "parentId": 101, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3130, "timestamp": 703343384833, "id": 113, "parentId": 101, "tags": {"name": "150.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3128, "timestamp": 703343384836, "id": 114, "parentId": 101, "tags": {"name": "814.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3126, "timestamp": 703343384838, "id": 115, "parentId": 101, "tags": {"name": "73.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3125, "timestamp": 703343384839, "id": 116, "parentId": 101, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 3123, "timestamp": 703343384841, "id": 117, "parentId": 101, "tags": {"name": "933.js", "cache": "HIT"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 11980, "timestamp": 703343384843, "id": 118, "parentId": 101, "tags": {"name": "97.js", "cache": "MISS"}, "startTime": 1751535343750, "traceId": "20e66b668228180c"}, {"name": "minify-webpack-plugin-optimize", "duration": 15228, "timestamp": 703343381607, "id": 101, "parentId": 16, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1751535343747, "traceId": "20e66b668228180c"}, {"name": "css-minimizer-plugin", "duration": 82, "timestamp": 703343396899, "id": 119, "parentId": 16, "tags": {}, "startTime": 1751535343762, "traceId": "20e66b668228180c"}, {"name": "create-trace-assets", "duration": 1058, "timestamp": 703343397082, "id": 120, "parentId": 17, "tags": {}, "startTime": 1751535343762, "traceId": "20e66b668228180c"}, {"name": "seal", "duration": 88884, "timestamp": 703343312636, "id": 88, "parentId": 16, "tags": {}, "startTime": 1751535343678, "traceId": "20e66b668228180c"}, {"name": "webpack-compilation", "duration": 483490, "timestamp": 703342925423, "id": 16, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751535343290, "traceId": "20e66b668228180c"}, {"name": "emit", "duration": 4763, "timestamp": 703343409136, "id": 121, "parentId": 13, "tags": {}, "startTime": 1751535343774, "traceId": "20e66b668228180c"}, {"name": "webpack-close", "duration": 35642, "timestamp": 703343414424, "id": 122, "parentId": 13, "tags": {"name": "server"}, "startTime": 1751535343779, "traceId": "20e66b668228180c"}, {"name": "webpack-generate-error-stats", "duration": 1378, "timestamp": 703343450101, "id": 123, "parentId": 122, "tags": {}, "startTime": 1751535343815, "traceId": "20e66b668228180c"}, {"name": "make", "duration": 88, "timestamp": 703343455760, "id": 125, "parentId": 124, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "chunk-graph", "duration": 21, "timestamp": 703343456121, "id": 127, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "optimize-modules", "duration": 3, "timestamp": 703343456163, "id": 129, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "optimize-chunks", "duration": 25, "timestamp": 703343456189, "id": 130, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "optimize-tree", "duration": 3, "timestamp": 703343456231, "id": 131, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "optimize-chunk-modules", "duration": 24, "timestamp": 703343456258, "id": 132, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "optimize", "duration": 141, "timestamp": 703343456153, "id": 128, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "module-hash", "duration": 6, "timestamp": 703343456374, "id": 133, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "code-generation", "duration": 5, "timestamp": 703343456384, "id": 134, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "hash", "duration": 23, "timestamp": 703343456406, "id": 135, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "code-generation-jobs", "duration": 14, "timestamp": 703343456429, "id": 136, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "module-assets", "duration": 6, "timestamp": 703343456440, "id": 137, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "create-chunk-assets", "duration": 9, "timestamp": 703343456449, "id": 138, "parentId": 126, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "minify-js", "duration": 14, "timestamp": 703343461301, "id": 140, "parentId": 139, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1751535343826, "traceId": "20e66b668228180c"}, {"name": "minify-webpack-plugin-optimize", "duration": 1078, "timestamp": 703343460242, "id": 139, "parentId": 124, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1751535343825, "traceId": "20e66b668228180c"}, {"name": "css-minimizer-plugin", "duration": 3, "timestamp": 703343461345, "id": 141, "parentId": 124, "tags": {}, "startTime": 1751535343826, "traceId": "20e66b668228180c"}, {"name": "seal", "duration": 6072, "timestamp": 703343456065, "id": 126, "parentId": 124, "tags": {}, "startTime": 1751535343821, "traceId": "20e66b668228180c"}, {"name": "webpack-compilation", "duration": 7204, "timestamp": 703343454977, "id": 124, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751535343820, "traceId": "20e66b668228180c"}, {"name": "emit", "duration": 437, "timestamp": 703343462202, "id": 142, "parentId": 13, "tags": {}, "startTime": 1751535343827, "traceId": "20e66b668228180c"}, {"name": "webpack-close", "duration": 67, "timestamp": 703343462740, "id": 143, "parentId": 13, "tags": {"name": "edge-server"}, "startTime": 1751535343828, "traceId": "20e66b668228180c"}, {"name": "webpack-generate-error-stats", "duration": 192, "timestamp": 703343462809, "id": 144, "parentId": 143, "tags": {}, "startTime": 1751535343828, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 39579, "timestamp": 703343466416, "id": 158, "parentId": 146, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 39585, "timestamp": 703343466419, "id": 160, "parentId": 146, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 39586, "timestamp": 703343466420, "id": 161, "parentId": 146, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 16810, "timestamp": 703343532085, "id": 162, "parentId": 145, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/PortalLayoutContent.tsx", "layer": "app-pages-browser"}, "startTime": 1751535343897, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 99389, "timestamp": 703343466401, "id": 150, "parentId": 146, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error&page=%2F_not-found%2Fpage!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 104459, "timestamp": 703343466403, "id": 151, "parentId": 146, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 117663, "timestamp": 703343466406, "id": 153, "parentId": 146, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 6026, "timestamp": 703343592669, "id": 165, "parentId": 162, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/Header.tsx", "layer": "app-pages-browser"}, "startTime": 1751535343958, "traceId": "20e66b668228180c"}, {"name": "build-module-tsx", "duration": 7482, "timestamp": 703343592968, "id": 166, "parentId": 162, "tags": {"name": "/Users/<USER>/Documents/Laplace/Projects/nextjs-dev/ai_cagent_b2b/src/components/SidebarNavigation.tsx", "layer": "app-pages-browser"}, "startTime": 1751535343958, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 140638, "timestamp": 703343466399, "id": 149, "parentId": 146, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 142265, "timestamp": 703343466415, "id": 157, "parentId": 146, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fabadr%2FDocuments%2FLaplace%2FProjects%2Fnextjs-dev%2Fai_cagent_b2b%2Fsrc%2Fapp%2F(portal)%2Fdashboard%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}, {"name": "add-entry", "duration": 142306, "timestamp": 703343466393, "id": 148, "parentId": 146, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1751535343831, "traceId": "20e66b668228180c"}]