# B2B Portal - Next.js E-commerce Application

A comprehensive B2B E-commerce Portal built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Stack**: Next.js 15 with App Router, TypeScript, Tailwind CSS
- **Internationalization**: Multi-language support (English/Arabic) with RTL
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Type Safety**: Full TypeScript integration
- **Code Quality**: ESLint, Prettier, and strict TypeScript configuration
- **Performance**: Optimized builds with Next.js 15 features

## 📋 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── contracts/         # Contract management pages
│   ├── dashboard/         # Analytics dashboard
│   ├── orders/           # Order management pages
│   ├── products/         # Product catalog pages
│   ├── profile/          # User profile pages
│   ├── globals.css       # Global styles
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/           # React components
│   └── ui/              # Base UI components
├── contexts/            # React contexts
├── lib/                 # Core utilities
│   ├── api/            # API integration
│   ├── i18n/           # Internationalization
│   └── redux/          # Redux store
├── types/              # TypeScript type definitions
└── utils/              # Utility functions

public/
├── locales/            # Translation files
│   ├── en/            # English translations
│   └── ar/            # Arabic translations
└── images/            # Static images
```

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ai_cagent_b2b
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Run the development server**
   ```bash
   bun run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📜 Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run lint` - Run ESLint
- `bun run lint:fix` - Fix ESLint issues
- `bun run format` - Format code with Prettier
- `bun run format:check` - Check code formatting
- `bun run type-check` - Run TypeScript type checking

## 🌐 Internationalization

The application supports multiple languages:

- **English** (en) - Default language
- **Arabic** (ar) - RTL support included

Translation files are located in `public/locales/[locale]/common.json`.

## 🎨 Styling

- **Tailwind CSS** for utility-first styling
- **Custom CSS variables** for theming
- **RTL support** for Arabic language
- **Responsive design** with mobile-first approach

## 📦 Dependencies

### Core Dependencies
- Next.js 15+ - React framework
- React 18+ - UI library
- TypeScript - Type safety
- Tailwind CSS - Styling

### Additional Libraries
- next-intl - Internationalization
- @reduxjs/toolkit - State management
- react-redux - Redux React bindings
- lucide-react - Icons
- framer-motion - Animations
- @headlessui/react - Accessible UI components

## 🔧 Configuration

### TypeScript
- Strict mode enabled
- Path aliases configured (`@/*`)
- Next.js plugin integration

### ESLint
- Next.js recommended rules
- TypeScript integration
- Import ordering rules

### Prettier
- Tailwind CSS plugin
- Consistent code formatting
- Single quotes, semicolons

## 📈 Development Roadmap

### Phase 1: Foundation ✅
- [x] Project Setup
- [ ] Core Architecture
- [ ] Base UI Components

### Phase 2: Structure and Infrastructure
- [ ] Layout Components
- [ ] Internationalization
- [ ] Mock API Integration

### Phase 3: Core Features
- [ ] Product Catalog Components
- [ ] Order Management Components
- [ ] User Profile and Authentication
- [ ] Contract Management

### Phase 4: Advanced Features
- [ ] Dashboard and Analytics

### Phase 5: Quality and Deployment
- [ ] Testing Implementation
- [ ] Performance Optimization
- [ ] Accessibility Implementation
- [ ] Deployment and DevOps
- [ ] Security Implementation
- [ ] Documentation and Knowledge Transfer

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions, please refer to the project documentation or create an issue in the repository.
