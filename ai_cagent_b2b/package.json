{"name": "b2b-portal", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@headlessui/react": "^1.7.0", "@reduxjs/toolkit": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "framer-motion": "^11.0.0", "lucide-react": "^0.400.0", "next": "^15.0.0", "next-intl": "^3.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-redux": "^9.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.4.0", "typescript": "^5.0.0"}}